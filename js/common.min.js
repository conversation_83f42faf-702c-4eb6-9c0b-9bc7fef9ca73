$.cookie = function(key, value, options) {
	if (arguments.length > 0x1 && String(value) !== "[object Object]") {
		if (value === null || value === undefined) {
			options.expires = -0x1
		};
		if (typeof options.expires === "number") {
			var days = options.expires,
			t = options.expires = new Date();
			t.setDate(t.getDate() + days)
		};
		value = String(value);
		return (document.cookie = [encodeURIComponent(key), "=", options.raw ? value: encodeURIComponent(value), options.expires ? "; expires=" + options.expires.toUTCString() : "", options.path ? "; path=" + options.path: "", options.domain ? "; domain=" + options.domain: "", options.secure ? "; secure": ""].join(""))
	};
	options = value || {};
	var result, decode = options.raw ?
	function(s) {
		return s
	}: decodeURIComponent;
	return (result = new RegExp("(?:^|; )" + encodeURIComponent(key) + "=([^;]*)").exec(document.cookie)) ? decode(result[0x1]) : null
};

Date.prototype.pattern = function(fmt) {
	var o = {
		"M+": this.getMonth() + 1,
		//月份           
		"d+": this.getDate(),
		//日           
		"h+": this.getHours() % 12 == 0 ? 12 : this.getHours() % 12,
		//小时           
		"H+": this.getHours(),
		//小时           
		"m+": this.getMinutes(),
		//分           
		"s+": this.getSeconds(),
		//秒           
		"q+": Math.floor((this.getMonth() + 3) / 3),
		//季度           
		"S": this.getMilliseconds() //毫秒           
	};
	//y年 M月 d日 h12时 H24时 m分 s秒 q季度 S毫秒 E周几
	var week = {
		"0": "/u65e5",
		"1": "/u4e00",
		"2": "/u4e8c",
		"3": "/u4e09",
		"4": "/u56db",
		"5": "/u4e94",
		"6": "/u516d"
	};
	if (/(y+)/.test(fmt)) {
		fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
	}
	if (/(E+)/.test(fmt)) {
		fmt = fmt.replace(RegExp.$1, ((RegExp.$1.length > 1) ? (RegExp.$1.length > 2 ? "/u661f/u671f": "/u5468") : "") + week[this.getDay() + ""]);
	}
	for (var k in o) {
		if (new RegExp("(" + k + ")").test(fmt)) {
			fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
		}
	}
	return fmt;
}
// 星期格式转换 0-6：周日到周六
function weekFormat(e, prefix = "周") {
    switch (e) {
        case 0:
            return prefix + "日"
            break;
        case 1:
            return prefix + "一"
            break;
        case 2:
            return prefix + "二"
            break;
        case 3:
            return prefix + "三"
            break;
        case 4:
            return prefix + "四"
            break;
        case 5:
            return prefix + "五"
            break;
        case 6:
            return prefix + "六"
            break;
        default:
            return ""
            break;
    }
}
// 转换今日的时辰格式
function todayTimeFormat(e) {
    if (e >= 0 && e < 7) {
        return "凌晨"
    } else if (e >= 7 && e < 11) {
        return "上午"
    } else if (e >= 11 && e < 13) {
        return "中午"
    } else if (e >= 13 && e < 18) {
        return "下午"
    } else if (e >= 18 && e < 24) {
        return "晚上"
    } else {
        return ""
    }
}

// 是否显示周几
function isShowWeekDay(sub, weekDay) {
    const currentWeekDay = new Date().getDay()
    const dayTime = 1000 * 60 * 60 * 24
    // 1.当前时间与消息时间相差必须大于2天小于7天
    // 2.当前时间距离本周一相差必须大于2天且小于当前距离周一的天数
    // 3.消息时间不可能是0-周日，因为周日没有给后面时间留空间，不会走这里的逻辑，而是走今天的逻辑
    if (sub >= dayTime * 2 && weekDay !== 0 && sub <= dayTime * currentWeekDay) {
        return true
    } else {
        return false
    }
}


// 仿微信时间显示格式转换 @time 时间戳毫秒
function wechatTimeFormat(time) {
    const today = new Date().getTime()
    // 当前时间减去获取到的时间
    const sub = today - time
    const day = 1000 * 60 * 60 * 24
    const timeDate = new Date(time)
    const currentYear = new Date().getFullYear()
    const getYear = new Date(time).getFullYear()
    const HHmm = `${formatTime2("hh", timeDate)}:${formatTime2("mm", timeDate)}`
    const showWeekDay = isShowWeekDay(sub, timeDate.getDay())
    const currentDay = new Date().getDate()
    const getDay = new Date(time).getDate()
    const subDay = currentDay - getDay
    if (currentYear > getYear) {
        return `${formatTime2("yyyy-MM-dd", timeDate)} ${formatTime2("hh:mm", timeDate)}`
        // 上午下午
        return `${formatTime2("yyyy年MM月dd日", timeDate)} ${todayTimeFormat(timeDate.getHours())} ${formatTime2("hh:mm", timeDate)}`
    } else if (showWeekDay) {
        return `${weekFormat(timeDate.getDay())} ${HHmm}`
        //} else if (sub > day && sub < day * 2) {
    } else if (subDay === 1) {
        return `昨天 ${HHmm}`
    } else if (sub <= day) {
        return HHmm
    } else {
        return `${formatTime2("MM-dd", timeDate)} ${formatTime2("hh:mm", timeDate)}`
        // 上午下午
        return `${formatTime2("MM月dd日", timeDate)} ${todayTimeFormat(timeDate.getHours())} ${formatTime2("hh:mm", timeDate)}`
    }
}

function formatTime2(fmt, timestamp) {
    var date = new Date(timestamp) // 兼容safari
    var o = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'h+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
        'q+': Math.floor((date.getMonth() + 3) / 3),
        'S': date.getMilliseconds()
    }
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
    }
    for (var k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
        }
    }
    return fmt
}