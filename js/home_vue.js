// 缩放
scale()

$(window).resize(function() {
    scale()
})

// 缩放方法
function scale() {
    // 获取屏幕宽高
    var screenH = $(window).height()
    var screenW = $(window).width()
    var scale = screenW / 1920
    $("body").css({ transform: 'scale(' + scale + ')' });
    //console.log(document.body.clientHeight);
    $("html").css({ height: screenH })
}



// 挂载VUE
new Vue({
    //    此处指向vue组件所要渲染的DOM标签 <div id="box"></div>   el:'#box'  用id属性标识 也可用class属性
    // 如果是class <div class="box"></div>   el:'.box' 
    // 总之最外层div用class或id绑定js ，下面el对应body对外层div选择器名称，绑定之后才可以使用elementui中组件内容
    el: '#home',
    data() {
        return {
            search_input4: '',
            content_center_date: '',
            content_center_date_show_is: false,
            content_center_content: true,
            content_center_content_pop_show_is: [1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            news_send_textarea: '',
            news_send_input: '',
            news_send_radio: '1',
            sizeForm: {
                date1: ''
            },
            radio: '1',
            input: '',
            checked: true,
            check: false,
            home_msg: false,
            my_center_mgs: true,
            my_center_pass: false,
            ppt_show_ppt: false,
            ppt_show_show: true,
            login: {
                name: '',
                youxiang: '',
                tel: '',
                pass: '',
                duanxin: '',
                company: ''
            },
            dialogVisible: false
        }
    },
    methods: {
        content_center_date_show(boolen) {
            this.content_center_date_show_is = boolen
        },
        content_center_content_pop_show(index) {
            let data = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
            if (this.content_center_content_pop_show_is[index] == 1) {
                data[index] = 0
                this.content_center_content_pop_show_is = data
            } else {
                data[index] = 1
                this.content_center_content_pop_show_is = data
            }
        },
        goto(url) {
            window.location.href = url
        },
        handleClose(done) {
            // this.$confirm('确认关闭？')
            //     .then(_ => {
            //         done();
            //     })
            //     .catch(_ => {});
        }
    }
})