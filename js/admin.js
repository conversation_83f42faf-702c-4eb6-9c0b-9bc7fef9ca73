var centerConfig = {
	isgqbj: 0,
	kaipanStatus: 0,
	applyscorelastid: null,
	nownotesid: null,
	jifenchangeid: -1,
	jifenCache: {
		"index": 0
	},
	voice: false,
	wangpanlogined: false,
	ishidesuccessmsg: false,
	autoUpload: false,
	hoverCache: '',
};

$(document).ready(function() {
	//铃声切换
	(function() {
		var lsTag = $("#lingsheng");
		var val = $.cookie("lingsheng");
		lsTag.text(val == "2" ? "声音2": "声音1");
		lsTag.click(function() {
			$.cookie("lingsheng", (val == "2" ? "1": "2"), {
				"expires": 365 * 50
			});
			location.reload();
		});
	} ());
	
	//初始化
// 	$("#open")[0].style.display = 'block';
// 	$("#main")[0].style.display = 'none';
    $(document.documentElement).one("touchstart click",
        function() {
        	$('audio').each(function() {
        		this.load();
        		centerConfig.voice = true;
        	});
        // 	$("#open")[0].style.display = 'none';
        // 	$("#main")[0].style.display = 'block';
        }
    );
    
    var inputData = {
        PeiLv:{type: "setpeilv",val: ''},
        maxbet:{type: "setlimitbet",val: '',bettype: 0},
        minbet:{type: "setlimitbet",val: '',bettype: 1},
        HoldTimeSeconds:{type: "setholdtimeseconds",val: ''},
        DelayUpload:{type: "setdelayupload",val: ''},
        AutoTime:{type: "setautotime",val: ''},
        AutoOdds:{type: "setautoodds",val: ''},
        HideRank:{type: "sethiderank",val: ''},
    };
    
    $(".inpchangeval").each(function() {
        let id = $(this).attr("id");
        inpchangeval("#" + id, id,function(val, elem) {
            let data = inputData[id];
            data.val = val;
        	$(elem).attr("disabled", "disabled");
        	$.ajax({
        		url: "/admin/group/setconfig",
        		dataType: "json",
        		data: data,
        		cache: false,
        		success: function(data) {
        			try {
        				if (data && data.Success && data.Err == 0) {
        					$(elem).val(val);
        					return;
        				}
        			} catch(e) {
        			    
        			} finally {
        				$(elem).removeAttr("disabled");
        			}
        		},
        		error: function() {
        			$(elem).removeAttr("disabled");
        		}
        	});
        }); 	
	});
	checkboxAutoAjax("#AutoUnilateral", "AutoUnilateral",function() {
		var val = $("#AutoUnilateral:checked:first").length;
		return "/admin/group/setconfig?type=setautounilateral&val=" + val;
	});
	
	checkboxAutoAjax("#AutoType", "AutoType",function() {
		var val = $("#AutoType:checked:first").length;
		return "/admin/group/setconfig?type=setautotype&val=" + val;
	});
	
	checkboxAutoAjax("#hidesuccessmsg", "IsHideSuccessMessage",function() {
		var val = $("#hidesuccessmsg:checked:first").length;
		return "/admin/group/setconfig?type=sethidesuccesmsg&val=" + val;
	});
	checkboxAutoAjax("#HideRebate", "HideRebate",function() {
		var val = $("#HideRebate:checked:first").length;
		return "/admin/group/setconfig?type=sethiderebate&val=" + val;
	});
	
	checkboxAutoAjax("#HistoryType", "HistoryType",function() {
		var val = $("#HistoryType:checked:first").length;
		return "/admin/group/setconfig?type=sethistorytype&val=" + val;
	});
	
	checkboxAutoAjax("#selectupw", "IsSelectUPW",function() {
		var val = $("#selectupw:checked:first").length;
		return "/admin/game/setSelectUPW?val=" + val;
	});
	var ldChangeUser = null;
	$(".jifenright .nickname input,.jifenright .remark input,.jifenright .fanshui input").bind("input",
	function() {
		var datatype = $(this).attr("data-type");
		var val = $(this).val(); (function() {
			var type = datatype,
			v = val,
			user = selectChangejifen;
			clearTimeout(ldChangeUser);
			if (((!v) && type != "remark") || (!user) || (!type)) {
				return;
			}
			var wxid = user.wxid;
			var uid = user.uid;
			if ((!wxid)) {
				return;
			}
			ldChangeUser = setTimeout(function() {
				$.ajax({
					url: "/admin/user/setvipinfo",
					dataType: "json",
					data: {
						"type": "setvipinfo",
						"vname": wxid,
						"mode": type,
						"val": v,
						"uid": uid,
					},
					cache: false,
					success: function(data) {
						//try{
						if (data && data.Success && data.Err == 0) {
							user[type] = v;
							$(".jifenright .jifenmessage").text({
								"nickName": "名字",
								"remark": "备注",
								"fanshui": "返水"
							} [type] + "改为：" + v);
						}
					},
					error: function() {
					    
					}
				});
			},
			500);
		})();
	});
	
	$("#baojingbtn").click(function() {
		baojing(!isBaojing);
	});
	
	//setInterval(()=>{
	//    autoHandle();
	//}, 1500);
	
	//修改密码
	(function() {
		$(".toppanelmenu span:contains('修改密码')").click(function() {
			$(".divchangepwd").show();
			$(".divchangepwd [name=oldpwd]")[0].focus();
			$(".divchangepwd [name=oldpwd]")[0].select();
		});
		$(".divchangepwd button:contains('取消')").click(function() {
			$(".divchangepwd [name=oldpwd]").val("");
			$(".divchangepwd [name=newpwd]").val("");
			$(".divchangepwd").hide();
		});
		$(".divchangepwd button:contains('修改')").click(function() {
			var tag = $(this);
			if (tag.attr("disabled")) {
				return;
			}
			var oldpwd = $(".divchangepwd [name=oldpwd]").val();
			var newpwd = $(".divchangepwd [name=newpwd]").val();
			tag.attr("disabled", "disabled");
			$.ajax({
				type: "post",
                url: "/Admin/Admin/FunPassEdit2",
				data: "oldpwd=" + encodeURIComponent(oldpwd) + "&newpwd=" + encodeURIComponent(newpwd),
				success: function(data) {
					tag.removeAttr("disabled");
					if (data) {
						if (data.code == 0) {
							alert("请记住新密码。" + (data.msg || ""));
							$(".divchangepwd [name=oldpwd]").val("");
							$(".divchangepwd [name=newpwd]").val("");
							$(".divchangepwd").hide();
						} else {
                            alert("密码修改失败。" + (data.msg || ""));
						}
					}
				},
				error: function() {
					tag.removeAttr("disabled");
				}
			});
			return false;
		});
	})();

	$("#autouploadwangpan").change(function() { (function() {
			$("#autouploadwangpan").attr("disabled", "disabled");
			var isupload = $("#autouploadwangpan:checked:first").length;
			var site_info = get_select_checked(".wangpanselect [name='site']");
			var valicode  = site_info.attr('valicode');
			$.ajax({
				url: "/admin/game/setwangpan",
				dataType: "json",
				cache: false,
				success: function(data) {
					try {
            			if(valicode == 0){
            			    $(".wangpanlogin .yzm").hide();
            			}else{
            			    $(".wangpanlogin .yzm").show();
            			}
            			$("#autouploadwangpan").removeAttr("disabled");
						if (data && data.Success && data.Err == 0) {
                			if (isupload && data.Err == 0) {
                				$(".wangpanselect [name='site']").attr("disabled", "disabled");
								$(".wangpanlogin").show();
								$(".wangpanlogin [name='username']")[0].focus();
								$(".wangpanlogin [name='username']")[0].select();
								centerConfig.autoUpload = true;
						        $("#autouploadwangpan").attr("checked", true);
                			} else {
								centerConfig.autoUpload = false;
								$(".wangpanlogin").hide();
                				$(".wangpanselect [name='site']").removeAttr("disabled");
                			}
						}
					} catch(e) { 
					    
					}
				},
				error: function() {
					$("#autouploadwangpan").removeAttr("disabled");
					$("#autouploadwangpan").attr("checked", !isupload);
				}
			});
		})();
	});

	
	$(".wangpanlogin form [name='username']").bind("blur",function() {
	    getSiteValiCode();

	});
	
	$(".wangpanlogin .yzm s").click(function() {
	    getSiteValiCode();
	});
	
	var getSiteValiCode = function(){
		var site_info = get_select_checked(".wangpanselect [name='site']");
		var site_code = site_info.attr('site-code');
	    var valicode  = site_info.attr('valicode');
		var username  = $(".wangpanlogin form [name='username']").val();
	    if(valicode == 1 && username != ''){
    		$.ajax({
    			url: "/admin/game/getsitevalicode?site_code=" + site_code + "&username=" + username,
    			dataType: "json",
    			cache: false,
    			success: function(data) {
    			    if(data.error_code == 0){
                        $(".wangpanlogin .yzm s").html("").append($("<img src='" + data.url + "'/>"));
                		if ($(".wangpanlogin .yzm:visible").length) {
                			$(".wangpanlogin form [name='valicode']").val("")[0].focus();
                		}
    			    }else{
    			        alert(data.error_msg);
    			    }
    			},
    			error: function() {
    			    
    			}
    		});
	    }
	}
	
	$(".wangpanlogin form").bind("submit",function() {
		var site_info = get_select_checked(".wangpanselect [name='site']");
		var site_code = site_info.attr('site-code');
		var site_bian = site_info.attr('site-bian');
		var site_index= site_info.attr('site-index');
	    var valicode  = site_info.attr('valicode');
		var tag = $(".wangpanlogin form button[type='submit']");
		if (tag.attr("disabled")) {
			return;
		}
		tag.attr("disabled", "disabled");
		$.ajax({
			type: "post",
			url: "/admin/game/loginwangpan?site_code=" + site_code + "&site_index=" + site_index + "&site_bian=" + site_bian,
			data: $(".wangpanlogin form").serialize(),
			success: function(data) {
				tag.removeAttr("disabled");
				if (data) {
				    switch (data.error_code) {
				        case 0:
    						$(".wangpanlogin").hide();
    						$("#autouploadwangpan").prop("checked", true);
    						$(".wangpanselect [name='site']").attr("disabled", "disabled");
    						centerConfig.wangpanlogined = true;
				            break;
				        default:
				            alert(data.error_msg);
				            getSiteValiCode();
				    }
				}
			},
			error: function() {
				tag.removeAttr("disabled");
			}
		});
		return false;
	});
	
	selectJifen();
	$(".jifentitle .refjifen").click(function() {

		centerConfig.jifenCache = {
			"index": 0
		};
		$(".jifenleftlist").html("");
		updateJifenList();
	});
	$(".jifentitle .autowater").click(function() {
		if (!window.confirm("是否结算群聊所有人的反水")) {
			return;
		}
		$(".jifentitle .autowater").attr("disabled", "disabled");
		$.ajax({
			url: "/admin/group/autowater",
			dataType: "json",
			cache: false,
			success: function(data) {
				$(".jifentitle .autowater").removeAttr("disabled");
				alert(data.Message)
			},
			error: function() {
				$(".jifentitle .autowater").removeAttr("disabled");
			}
		});
	});
	
	$(".jifentitle .clearjifenliushuinote").click(function() {
		if (!window.confirm("是否清空所有人流水记录？不包括积分")) {
			return;
		}
		$(".jifentitle .clearjifenliushuinote").attr("disabled", "disabled");
		$.ajax({
			url: "/admin/group/clearallscoreliushui",
			dataType: "json",
			cache: false,
			success: function(data) {
				if (data && data.Success) {
					if (data.Err == 0) {
						location.reload();
					}
				}
				$(".jifentitle .clearjifenliushuinote").removeAttr("disabled");
			},
			error: function() {
				$(".jifentitle .clearjifenliushuinote").removeAttr("disabled");
			}
		});
	});

	$(".jifenright label").click(function() {
		$(".jifenright [name='changejifenval']")[0].focus();
		$(".jifenright [name='changejifenval']")[0].select();
	});

	$(".jifenright .headimg").click(function() {
		var s = selectChangejifen;
		if (s == null) {
			return;
		}
		if (!window.confirm("是否随机换头像？")) {
			return;
		}
		$(".jifenright .headimg").attr("disabled", true);

		$.ajax({
			url: "/admin/user/changerandomhead?uid=" + s.uid,
			dataType: "json",
			cache: false,
			success: function(data) {
				if (data && data.Success) {
					if (data.Err == 0) {
						$(".jifenright .headimg").attr("src", data.imgUrl);
						selectChangejifen.headimg = data.imgUrl;
					} else {
						$(".jifenright .jifenmessage").text(data.imgUrl);
					}
				}
				$(".jifenright .headimg").removeAttr("disabled");
			},
			error: function() {
				$(".jifenright .headimg").removeAttr("disabled");
			}
		});
	});

	$(".jifenright b:contains('修改积分')").click(function() {
		if (!centerConfig.isCancelAutotuoChangeScore) {
			centerConfig.isCancelAutotuoChangeScore = 1;
			$(this).css("color", "red");
		} else {
			centerConfig.isCancelAutotuoChangeScore = 0;
			$(this).css("color", "#000000");
		}
	});

	$(".jifenright .submitjifen").click(function() {
		var s = selectChangejifen;
		if (s == null) {
			return;
		}
		$(".jifenright .submitjifen").attr("disabled", true);

		$.ajax({
            url: "/User/UpdateUserMoney?id=" + s.uid + "&type=" + $(".jifenright [name='htype']:checked").val() + "&points=" + $(".jifenright [name='changejifenval']").val(),
            dataType: "json",
            type: "post",
			cache: false,
			success: function(data) {
				if (data) {
					if (data.code == 0) {
						selectJifen();
					}
					$(".jifenright .jifenmessage").text(data.msg);
				}
				$(".jifenright .submitjifen").removeAttr("disabled");
			},
			error: function() {
				$(".jifenright .submitjifen").removeAttr("disabled");
				$(".jifenright .jifenmessage").text(data.msg);
			}
		});
	});

	$(".jifenright .create_user").click(function() {
		$(".jifenright .create_user").attr("disabled", true);
		$.ajax({
            url: "/User/CreateUser",
            data: {
                UserID: $("#aid").val()
            },
            type: 'post',
			dataType: "json",
			cache: false,
			success: function(data) {
				if (data) {
                    if (data.code == 0) {
						var s = data.data;
						$(".jifenright .jifenmessage").text("创建客户成功，" + s.nickName);
						selectJifen(s);
						$(".jifenleftlist").html("");
						updateJifenList();
					} else {
						$(".jifenright .jifenmessage").text("创建客户失败");
					}
				}
				$(".jifenright .create_user").removeAttr("disabled");
			},
			error: function() {
				$(".jifenright .create_user").removeAttr("disabled");
			}
		});
	});
	$("#kaipanstatus").click(function() {
		$("#kaipanstatus").attr("disabled", "disabled");
		var status = centerConfig.kaipanStatus == 1 ? 0 : 1;
		$.ajax({
			url: "/admin/group/setkaipan",
			dataType: "json",
			cache: false,
			success: function(data) {
				//try{
				if (data && data.Success && data.Err == 0) {
					centerConfig.kaipanStatus = status;
					$(".kaipanstatustit").removeClass("sed");
					if (status == 1 || data.isKp == 1) {
						$(".kaipanstatustit").addClass("sed");
						$("#kaipanstatus").text("开盘");
					} else {
						$("#kaipanstatus").text("收盘");
					}
				}
				$("#kaipanstatus").removeAttr("disabled");
			},
			error: function() {
				$("#kaipanstatus").removeAttr("disabled");
			}
		});
	});
});



function autoHandle() {
	$.ajax({
		url: "admin/group/needhandle",
		dataType: "json",
		cache: false,
		success: function(data) {
			if (data && data.error_code == 0) {
				changeUI(data);
			}else{
				window.location.reload();
				return;
			}
		},
		error: function() {
		}
	});
}

var nowUIName = null;
function changeUI(data) {
    if (data.OverdueTime && data.ServerTime) {
        var sjstr = new Date(data.OverdueTime).pattern("yyyy/MM/dd HH:mm:ss");
        if (sjstr != $(".guoqitime").text()) {
            $(".guoqitime").text(new Date(data.OverdueTime).pattern("yyyy/MM/dd HH:mm:ss"));
        }
        var sH = (data.OverdueTime - data.ServerTime) / (60 * 60 * 1000);
        if(sH <= 0){
            window.location.href = "/admin/logout";
        }else if(sH < 2){
            var xs = parseInt(sH);
            var fz = parseInt((sH - parseInt(sH)) * 60);
            if (centerConfig.isgqbj == 0) {
                centerConfig.isgqbj = sH;
                baojing(true, 3, "剩余" + xs + "小时" + (fz ? fz + "分钟" : "") + "过期");
            }
        }else{
            centerConfig.isgqbj = 0;
        }
    }
    
	var kaipanStatus = data.IsKaiPan ? 1 : 0;
	if (kaipanStatus != centerConfig.kaipanStatus) {
		centerConfig.kaipanStatus = kaipanStatus;
		$(".kaipanstatustit").removeClass("sed");
		if (kaipanStatus == 1) {
			$(".kaipanstatustit").addClass("sed");
			$("#kaipanstatus").text("开盘");
		} else {
			$("#kaipanstatus").text("收盘");
		}
	}

	if ($("#nowqun span").text() != (data.Chatroomname || "")) {
		$("#nowqun span").text(data.Chatroomname || "");
	}

	if (!centerConfig.run) {
		centerConfig.run = data.run;
	}
	if (centerConfig.run != data.run) {
		baojing(true, 8, "系统已升级，请重开游戏");
		centerConfig.run = data.run;
	}
	if (data.wangpanIsLogined != centerConfig.wangpanlogined) {
		centerConfig.wangpanlogined = data.wangpanIsLogined;
		if (data.wangpanIsLogined == true) {
			$("#autouploadwangpan").prop("checked", true);
			$(".wangpanlogin").hide();
			$("#baojingmsg").text("");
		} else {
			baojing(true, 1, "网盘掉线");
			$("#autouploadwangpan").removeAttr("checked");
			$(".wangpanlogin").hide();
		}

		var isupload = $("#autouploadwangpan:checked:first").length;
		if (isupload) {
			$(".wangpanselect [name='site']").attr("disabled", "disabled");
		} else {
			$(".wangpanselect [name='site']").removeAttr("disabled");
		}
	}
	set_select_checked(".wangpanselect [name='site']",'site-index', data.SiteIndex);
	if (data.jifenchangeid != centerConfig.jifenchangeid) {
		centerConfig.jifenchangeid = data.jifenchangeid;
		updateJifenList();
	}
	checkboxDataCheck(data);
	inpchangevalUICheck(data);

	$("#wangpanmessage").text(data.wangpanMessage || "");
	if (data.applyscorelastid != centerConfig.applyscorelastid) {
		centerConfig.applyscorelastid = data.applyscorelastid;
		//更新
		updateApplyScoreList();
	}
	if (data.nownotesid != centerConfig.nownotesid) {
		centerConfig.nownotesid = data.nownotesid;
		//更新
		updateNowNotes();
	}
	$("#kaipanstatus").css("visibility", "visible");
}


function AutoShangXiaFenTitle() {
	setTimeout(function() {
		$(".shangxiafentit").removeClass("sed");
		if ($(".sxflist div").length) {
			$(".shangxiafentit").addClass("sed");
		}
	},
	500);
}


window.updateNowNotesNumber = null;
window.lastnownotes = null;
var updateNowNotesCache = {};
var updateNowNotesCacheLen = 0;
var updateNowNotesTotal = 0;
var updateNowNotesTuoTotal = 0;
window.lastnownotes = null;

function set_select_checked(selectId, attrNode, checkValue) {
	var select = $(selectId)[0];
	for (var i = 0; i < select.options.length; i++) {
		if ($(select.options[i]).attr(attrNode) == checkValue) {
			$(select.options[i]).attr('selected',true);
			break;
		}
	}
}

function get_select_checked(selectId) {
	var select = $(selectId)[0];
	for (var i = 0; i < select.options.length; i++) {
		if (select.options[i].selected == true) {
		    return $(select.options[i]);
		}
	}
}

function updateNowNotes() {

	$.ajax({
		url: "/admin/order/getnownotes",
		dataType: "json",
		cache: false,
		success: function(data) {
			if (data && data.Success && data.Err == 0) {
				window.lastnownotes = data.Notes;
				if (data.Number != window.updateNowNotesNumber) {
					$(".xiazhubanlist").html("");
					$(".dangqianzhuqi").text(data.Number + "期");
					window.updateNowNotesNumber = data.Number;
					updateNowNotesCache = {};
				}
				updateNowNotesCacheLen = 0;
				updateNowNotesTotal = 0;
				updateNowNotesTuoTotal = 0;
				window.lastnownotes = data.Notes;
				var noteAutoShow = {};
				if (data.Notes && data.Notes.length) {
					for (var i = 0; i < data.Notes.length - 1; i++) {

						for (var j = 0; j < data.Notes.length - 1 - i; j++) {
							var a = data.Notes[j];
							var b = data.Notes[j + 1];
							if (parseInt(a.id) > parseInt(b.id)) {
								data.Notes[j + 1] = a;
								data.Notes[j] = b;
							}
						}
					}
					for (var k in data.Notes) { (function() {
							var note = data.Notes[k];
							var div = null;
							noteAutoShow[note.id] = true;
							var Total = 0;
							Total = note.total;
							if (note.IsTuo) {
								updateNowNotesTuoTotal += parseFloat(Total);
							} else {
								updateNowNotesTotal += parseFloat(Total);
							}
							if (!updateNowNotesCache[note.id]) {
								div = $("<div" + ((updateNowNotesCacheLen++) % 2 == 0 ? "": " class=\"se\"") + "><b>●</b><div class=\"wrap\"></div><s>×</s></div>");

								if (note.dm) {
									div.attr("title", note.dm.join(",") + "=" + note.ge + " 合计：" + Total);
								}
								updateNowNotesCache[note.id] = div;
								$(".wrap", div).text((note.IsTuo ? "(托)": "") + "[" + note.wxid + "," + note.nickName + "] “" + note.content + "” 合计：" + Total);
								$(div).addClass(note.IsTuo ? "tuo": "ren");
								$(".xiazhubanlist").prepend(div);

								(function() {
									var nt = note;
									var isReseting = false;

									$("b", div).click(function() {
										//1，4
										if ($(this).attr("status") == 0 || $(this).attr("status") == 2) {
											return;
										}
										if (isReseting) {
											return
										}
										isReseting = true;
										$.ajax({
											url: "/admin/game/submitToWp?id=" + nt.id,
											cache: false,
											dataType: "json",
											success: function(data) {
												isReseting = false;
												if (data && data.Success) {
													if (data.Err == 0) {
														return;
													}
												}
											},
											error: function() {
												isReseting = false;
											}
										});
									});

									$("s", div).click(function() {
										if (!window.confirm("确定撤销吗？" + $(".wrap", div).text())) {
											return;
										}
										if ($(this).attr("status") == 2) {
											return;
										}
										if (isReseting) {
											return
										}
										isReseting = true;
										$.ajax({
											url: "/admin/order/chedan?id=" + nt.id,
											cache: false,
											dataType: "json",
											success: function(data) {
												isReseting = false;
												if (data && data.Success) {
													if (data.Err == 0) {
														div.remove();
														return;
													}
												}
											},
											error: function() {
												isReseting = false;
											}
										});
									});
								})();
							} else {
								div = updateNowNotesCache[note.id];
							}
							$("b", div).attr("class", "");
							var statusTitle = "";
							switch (note.UWPStatus) {
							case 1:
								statusTitle = "正在入网";
								break;
							case 2:
								statusTitle = "入网成功";
								break;
							case 3:
								statusTitle = "入网失败";
								break;
							}
							$("b", div).attr("title", statusTitle);
							$("b", div).addClass("status" + note.UWPStatus);
							if (note.UWPStatus == 3 && $("b", div).attr("status") != note.UWPStatus) {
								baojing(true, 2, "入网失败");
								$(div).addClass('boder')
							}
							$("b", div).attr("status", note.UWPStatus);
						})();
					}
				}
				var totalMSG = "(普" + parseFloat(updateNowNotesTotal).toFixed(2) + "托" + parseFloat(updateNowNotesTuoTotal).toFixed(2) + ")"; // "总" + (updateNowNotesTotal + updateNowNotesTuoTotal) + 
				$(".dangqianzhuqi").text(data.Number + "期 " + totalMSG).attr("title", totalMSG);
				for (var k in updateNowNotesCache) {
					if (noteAutoShow[k]) {
						updateNowNotesCache[k].show();
					} else {
						updateNowNotesCache[k].hide();
					}
				}
			}
		},
		error: function() {}
	});
}
function updateApplyScoreList() {
	$.ajax({
		url: "/admin/order/applyscorelist",
		dataType: "json",
		cache: false,
		success: function(data) {
			if (data && data.Success && data.Err == 0) {
				var list = $.parseJSON(data.Message);
				$(".sxflist").html("");
				if (list && list.length) {
					$(".shangxiafentit").addClass("sed");
    			    $.each(list,function(index,value){
						if (value.isTuo) {
						    html = '<div class="sxfitem tuo '+ (index % 2 ? 'se' : '') +'" id="' + value.id + '"><span class="content"><i>【托】</i>'+ value.wxid + '：' + (value.type == 1 ? '上分 +': '下分 -') + parseInt(value.score) + ' (' + value.nickName  + ')' + '</span><div class="sxfbtns"><span class="ld"></span><span class="ty">同意</span><span class="qx">取消</span></div></div>';
						}else{
						    html = '<div class="sxfitem '+ (index % 2 ? 'se' : '') +'" id="' + value.id + '"><span class="content">'+ value.wxid + '：' + (value.type == 1 ? '上分 +': '下分 -') + parseInt(value.score) + ' (' + value.nickName  + ')' + '</span><div class="sxfbtns"><span class="ld"></span><span class="ty">同意</span><span class="qx">取消</span></div></div>';
						}
						$(".sxflist").append(html);
						if(index + 1 == list.length){
    						var trade = (!$.cookie("lingsheng") || $.cookie("lingsheng") == '1') ? $("#trade") : $("#trade_" + (value.type == 1 ? "in": "out") + "_pth");
        					if (trade.length && centerConfig.voice == true) {
        						trade[0].play();
        					}
						}
                    })
                    $(".ty").on('click', function(e){
                        var item = $(this).parent().parent();
                        $('.sxfbtns',item).attr("style", 'display:none');
                    	$.ajax({
                    		url: "/admin/order/agree?id=" + item.attr('id'),
                    		dataType: "json",
                    		cache: false,
                    		async:true,
                    		success: function(data) {
                    			if (data && data.Success) {
                    			    if(data.Err == 0){
                        				item.remove();
                        				AutoShangXiaFenTitle();
                        				return;
                    			    }
                    			    alert(data.Message);
                    			}
                                $('.sxfbtns',item).attr("style", 'display:block');
                    		},
                    		error: function() {
                    		    $('.sxfbtns',item).attr("style", 'display:block');
                    		}
                    	});
                    })
                    $(".qx").on('click', function(e){
                        var item = $(this).parent().parent();
                        $('.sxfbtns',item).attr("style", 'display:none');
                    	$.ajax({
                    		url: "/admin/order/disagree?id=" + item.attr('id'),
                    		dataType: "json",
                    		cache: false,
                    		async:true,
                    		success: function(data) {
                    			if (data && data.Success) {
                    			    if(data.Err == 0){
                        				item.remove();
                        				AutoShangXiaFenTitle();
                        				return;
                    			    }
                    			    alert(data.Message);
                    			}
                    		    $('.sxfbtns',item).attr("style", 'display:block');
                    		},
                    		error: function() {
                    		    $('.sxfbtns',item).attr("style", 'display:block');
                    		}
                    	});
                    })
				}
			}
		},
		error: function() {}
	});
}

var selectChangejifen = null;
function selectJifen(s) {
	selectChangejifen = s;
	var vname = "",
	nickname = "",
	score = "",
	jifenmessage = "",
	remark = "",
	fanshui = "";
	if (s) {
		vname = s.wxid;
		nickname = s.nickName;
		score = s.score;
		remark = s.remark;
		fanshui = s.fanshui;
	}
	$(".jifenright .usernumber").html("会员ID：");

	$(".jifenright .headimg").attr("src", "/images/defaultheadimg.jpg");
	if (s) {
		var vnamea = $("<a target=\"_blank\"></a>");
		vnamea.text(vname);
		vnamea.attr("href", "/Home/Enter?id=" + s.uid);
		$(".jifenright .usernumber").append(vnamea);
		$(".jifenright .headimg").attr("src", s.headimg ? s.headimg: "/images/defaultheadimg.jpg");
	}
	$(".jifenright .nickname input").val(nickname);
	$(".jifenright .remark input").val(remark);
	$(".jifenright .fanshui input").val(fanshui);

	$(".jifenright .score").text("积分：" + score);
	$("#jifen_xiafen").attr('checked', true);
	$(".jifenright [name='changejifenval']").val(score);
	$(".jifenright .jifenmessage").text(jifenmessage);
	$(".jifenright [name='changejifenval']")[0].focus();
	$(".jifenright [name='changejifenval']")[0].select();

}

function updateJifenList() {
	$.ajax({
        url: "/User/GetUserList",
        dataType: "json",
        type: 'post',
        data: {
            UserID: $("#aid").val()
        },
		cache: false,
        success: function (data) {
			if (!window.divJifen) {
				window.divJifen = $("<div style='display:none;position: absolute;border:1px solid #000;width:300px;height:240px;background:#fbfbfb;z-index:99999;top:360px;left:19px;padding:5px;'><div><div><b>真实用户总计:</b></div><div class='userjifenzj'></div></div><div><div><b>托总计:</b></div><div class='tuojifenzj'></div></div><hr><div><div><b>选择用户总计:</b></div><div class='dangqianjifenzj'></div></div></div>");
				$("div.toppanel").append(window.divJifen);
				$(document.documentElement).click(function() {
					window.divJifen.hide();
				}).bind("touchstart",
				function(event) {
					window.divJifen.hide();
				});
				window.divJifen.bind("touchstart",
				function(event) {
					event.stopPropagation();
				});
			}
			if (data && data.code == 0) {
				var list = data.data;
				$(".jifenleftlist").html("");
				if (list && list.length) {
					for (var i = 0; i < list.length - 1; i++) {
						for (var j = 0; j < list.length - 1 - i; j++) {
							var a = list[j];
							var b = list[j + 1];
							var aScore = a.score - (a.isTuo ? 1000000 : 0);
							var bScore = b.score - (b.isTuo ? 1000000 : 0);
							if (aScore < bScore) {
								list[j + 1] = a;
								list[j] = b;
							}
						}
					}
					var userjifenzjLiuShui = 0;
					var userjifenzjYinLi = 0;
					var userjifenzj = 0;
					var userkoushui = 0;
					var tuojifenzjLiuShui = 0;
					var tuojifenzjYinLi = 0;
					var tuouserjifenzj = 0;
					var tuokoushui = 0;

					for (var k in list) {
						var sB = list[k];
						var divB = null;
						if (centerConfig.hoverCache[sB.uid] == 1) {
							divB = $('<div><i class="h">托</i><u class="h">机</u><b></b><span></span><s>删除</s></div>');
						} else {
							divB = $('<div><i>托</i><u>机</u><b></b><span></span><s>删除</s></div>');
						}
						$("b", divB).text((sB.wxid + "").replace(/^[\s\S]*([\s\S]{5})$/, ".$1.") + "" + sB.nickName);
						$("span", divB).text(sB.score);
						if (sB.IsTuo) {
							$("i", divB).addClass("t");
							tuojifenzjLiuShui += parseFloat(sB.liuShui);
							tuojifenzjYinLi += parseFloat(sB.zongYingLi);
							tuouserjifenzj += parseFloat(sB.score);
							tuokoushui += parseFloat(sB.kouLiuShui);
						} else {
							userjifenzjLiuShui += parseFloat(sB.liuShui);
							userjifenzjYinLi += parseFloat(sB.zongYingLi);
							userjifenzj += parseFloat(sB.score);
							userkoushui += parseFloat(sB.kouLiuShui);
						}
						if (sB.IsAutoTuo) {
							$("u", divB).addClass("t");
						}
						divB.attr("title", sB.wxid + " (" + sB.nickName + ") " + "流水：" + parseFloat(sB.liuShui - sB.kouLiuShui) + "，总盈利：" + sB.zongYingLi + "，盈亏：" + (sB.zongYingLi - sB.liuShui) + "，下注期数：" + sB.xiaZhuQiShu); 
						(function() {
							var s = sB;
							var div = divB;
							$("i", div).click(function(event) {
								var tuoself = $(this);
								event.stopPropagation(); (function() {
									var tuoTag = tuoself;
									var tuoType = tuoTag.hasClass("t") ? 0 : 1;
									$.ajax({
										url: "/admin/user/settuo?id=" + s.uid,
										dataType: "json",
										cache: false,
										success: function(data) {
											if (data && data.code == 0) {
												tuoTag.removeClass("t");
												$(".sxfitem[vname='" + s.wxid + "']").removeClass("tuo");
												if (tuoType == 1) {
													tuoTag.addClass("t");
													$(".sxfitem[vname='" + s.wxid + "']").addClass("tuo");
												}
											} else {
												if (data.msg) {
													alert(data.msg)
												}
											}
										},
										error: function() {
										    
										}
									});
								})();
							});
							$("u", div).click(function(event) {
								var tuoself = $(this);
								event.stopPropagation(); (function() {
									var tuoTag = tuoself;
									var tuoType = tuoTag.hasClass("t") ? 0 : 1;
									$.ajax({
										url: "/admin/user/setautotuo?id=" + s.uid,
										dataType: "json",
										cache: false,
										success: function(data) {
											if (data && data.code == 0) {
												tuoTag.removeClass("t");
												$(".sxfitem[vname='" + s.wxid + "']").removeClass("autotuo");
												if (tuoType == 1) {
													tuoTag.addClass("t");
													$(".sxfitem[vname='" + s.wxid + "']").addClass("autotuo");
												}
											} else {
												if (data.msg) {
													alert(data.msg)
												}
											}
										},
										error: function() {
										    
										}
									});
								})();
							});
							$(div).hover(function(event) {
								$("i,u", div).addClass("h");
								centerConfig.hoverCache[s.uid] = 1;
							},
							function() {
								$("i,u", div).removeClass("h");
								centerConfig.hoverCache[s.uid] = 0;
							});
							$("s", div).click(function(event) {
								event.stopPropagation();
								var isConfirm = true;
								if (new Date().getTime() - (parseInt(window.lastDelUserTime) || 0) < 1000 * 60) {
									isConfirm = false;
								}
								if (isConfirm && window.confirm("是否删除" + s.wxid + " (" + s.nickName + ") " + "数据？") == false) {
									return;
								}
								window.lastDelUserTime = new Date().getTime();

								div.hide();
								$.ajax({
									url: "/admin/user/deleteuser?id=" + s.uid,
									dataType: "json",
									cache: false,
									success: function(data) {
                                        //try{
                                        if (data && data.code == 0) {
											div.remove();
											return;
										}else{
										    alert(data.msg);
										}
										div.show();
									},
									error: function() {
										div.show();
									}
								});
							});
							div.click(function(event) {
								event.stopPropagation();
								$(".dangqianjifenzj", window.divJifen).text(s.wxid + " (" + s.nickName + ") " + "流水：" + parseFloat(s.liuShui).toFixed(2) + "，总盈利：" + parseFloat(s.zongYingLi).toFixed(2) + "，盈亏：" + (parseFloat(s.zongYingLi) - parseFloat(s.kouLiuShui)).toFixed(2) + "，下注期数：" + s.xiaZhuQiShu);
								window.divJifen.show();
								selectJifen(s);
							}).bind("touchstart",
							function(event) {
								event.stopPropagation();
                                $(".dangqianjifenzj", window.divJifen).text(s.wxid + " (" + s.nickName + ") " + "流水：" + parseFloat(s.liuShui).toFixed(2) + "，总盈利：" + parseFloat(s.zongYingLi).toFixed(2) + "，盈亏：" + (parseFloat(s.zongYingLi) - parseFloat(s.kouLiuShui)).toFixed(2) + "，下注期数：" + s.xiaZhuQiShu);
								window.divJifen.show();
							});
							if (k % 2 == 1) {
								div.addClass("se");
							}
							$(".jifenleftlist").append(div);

						})();
					}

					$(".userjifenzj", window.divJifen).text("真流水：" + userjifenzjLiuShui.toFixed(2) + "，扣：" + userkoushui.toFixed(2) + "，盈利：" + parseFloat(userjifenzjYinLi).toFixed(2) + "，盈亏：" + (parseFloat(userjifenzjYinLi) - parseFloat(userkoushui)).toFixed(2) + "，积分：" + userjifenzj.toFixed(2));
					$(".tuojifenzj", window.divJifen).text("真流水：" + tuojifenzjLiuShui.toFixed(2) + "，扣：" + tuokoushui.toFixed(2) + "，盈利：" + parseFloat(tuojifenzjYinLi).toFixed(2) + "，盈亏：" + (parseFloat(tuojifenzjYinLi) - parseFloat(tuokoushui)).toFixed(2) + "，积分：" + tuouserjifenzj.toFixed(2));
				}
			}
		},
		error: function() {}
	});
}

var isBaojing = false;
var baojingmsg = {};
function baojing(isbj, id, msg) {
	isBaojing = isbj == true;
	try {
		$("#baojingbtn").removeClass("redbg");
		if (isBaojing) {
			$("#baojingbtn").addClass("redbg");
			if (id && msg) {
				baojingmsg[id] = msg;
			}
			var msg = "";
			for (var k in baojingmsg) {
				msg += (msg ? ",": "") + baojingmsg[k];
			}
			$("#baojingmsg").text(msg);
			$("#baojing")[0].load();
			$("#baojing")[0].play();
		} else {
			$("#baojing")[0].pause();
			$("#baojingmsg").text("");
			baojingmsg = {};
		}
	} catch(e) {}
}




var checkboxCache = {};
function checkboxDataCheck(data) {
	for (var k in checkboxCache) {
		if (data[k] != centerConfig[k]) {
			centerConfig[k] = data[k];
			if (data[k]) {
				$(checkboxCache[k]).attr("checked", "checked");
			} else {
				$(checkboxCache[k]).attr("checked", false);
			}
		}
	}
}
function checkboxAutoAjax(elemJqID, dataName, geturlFunc) {
	var elem = $(elemJqID);
	checkboxCache[dataName] = elemJqID;
	elem.change(function() { (function() {
			elem.attr("disabled", "disabled");
			var checked = $(elemJqID +":checked:first").length ? '' :'checked';
        	if (!window.confirm("是否确认更改？")) {
				elem.removeAttr("disabled");
				elem[0].checked = checked;
        		return;
        	}
			$.ajax({
				url: geturlFunc(),
				dataType: "json",
				cache: false,
				success: function(data) {
					try {
						if (data && data.Success) {
							if (data.Err == 0) {
								return;
							}
						}
						elem[0].checked = checked;
					} catch(e) {
					    
					} finally {
						elem.removeAttr("disabled");
					}
				},
				error: function() {
					elem.removeAttr("disabled");
				    elem[0].checked = checked;
				}
			});
		})();
	});
}

window.inpchangevalUI = {};
function inpchangevalUICheck(data) {
	for (var k in window.inpchangevalUI) {
		if ($(inpchangevalUI[k]).is(":disabled") == false && data[k] != centerConfig[k]) {
			centerConfig[k] = data[k];
			$(inpchangevalUI[k]).val(data[k]);
		}
	}
}

function inpchangeval(ele, dataname, callback) {
	inpchangevalUI[dataname] = ele;
	$(ele).removeClass("change err ok").addClass("ok").val(0);
	$(ele).bind("focus",function() {
		$(this).removeClass("change err ok");
		$(this).addClass("change");
		$(this)[0].focus();
		$(this)[0].select();
	});

	$(ele).bind("keydown",function(event) {
		if (event.keyCode == "13") {
		    $(this).blur();
		}
	});
	
	$(ele).bind("blur",function() {
	    switch (dataname) {
	        case 'AutoTime':
	            var val = $(this).val();
                var reg = /^(20|21|22|23|[0-1]\d):[0-5]\d:[0-5]\d$/;
                var regExp = new RegExp(reg);
                if(!regExp.test(val)){
                　　alert("时间格式不正确，正确格式为：00:00:00 至 23:59:59");
                　　return;
                }
	            break;
	        case 'minbet':
	            var val = parseFloat($(this).val()) || 0;
    	        if(val <= 10){
                　　alert("限注金额必须是大于0的正整数");
                　　return;
    	        }
	            break;
	        case 'PeiLv':
	            var val = parseFloat($(this).val()) || 0;
    	        if(val <= 0 || val > 100 ){
                　　alert("限注金额必须是大于0和小于等于100的正整数");
                　　return;
    	        }
	            break;
	        
	        default:
	            var val = parseFloat($(this).val()) || 0;
	            break;
	    }
		callback(val, ele);
		$(this).removeClass("change err ok").val(val).addClass("ok");

	});
}











//{
//    "error_code": 0,
//        "error_msg": "",
//            "Success": true,
//                "IsNeedLogin": false,
//                    "Err": 0,
//                        "Message": "[{"uid":15204,"wxid":"@v001","NickName":"001u65b0u5ba2u6237","headimg":"/index/image/defaultheadimg.jpg","remark":"","fanshui":"0.0000","score":"0.00","lastTime":"2020/12/07 21=>59=>23","IsTuo":false,"IsAutoTuo":false,"IsATClearScore":false,"LiuShui":0,"ZongYingKui":0,"ZongYingLi":0,"KouLiuShui":0,"XiaZhuQiShu":0},{"uid":15320,"wxid":"@v002","NickName":"002u65b0u5ba2u6237","headimg":"/index/image/defaultheadimg.jpg","remark":"","fanshui":"0.0000","score":"0.00","lastTime":"2020/12/07 21=>59=>23","IsTuo":false,"IsAutoTuo":false,"IsATClearScore":false,"LiuShui":0,"ZongYingKui":0,"ZongYingLi":0,"KouLiuShui":0,"XiaZhuQiShu":0},{"uid":15321,"wxid":"@v003","NickName":"003u65b0u5ba2u6237","headimg":"/index/image/defaultheadimg.jpg","remark":"dsafdsa","fanshui":"0.0000","score":"0.00","lastTime":"2020/12/07 21=>59=>23","IsTuo":false,"IsAutoTuo":false,"IsATClearScore":false,"LiuShui":0,"ZongYingKui":0,"ZongYingLi":0,"KouLiuShui":0,"XiaZhuQiShu":0},{"uid":15322,"wxid":"@v004","NickName":"004u65b0u5ba2u6237","headimg":"/static/image/avatar/user_6938.jpg","remark":"","fanshui":"0.0000","score":"0.00","lastTime":"2020/12/07 21=>59=>23","IsTuo":false,"IsAutoTuo":false,"IsATClearScore":false,"LiuShui":0,"ZongYingKui":0,"ZongYingLi":0,"KouLiuShui":0,"XiaZhuQiShu":0},{"uid":15329,"wxid":"@v005","NickName":"005u65b0u5ba2u6237","headimg":"/index/image/defaultheadimg.jpg","remark":"","fanshui":"0.0000","score":"0.00","lastTime":"2020/12/07 21=>59=>23","IsTuo":false,"IsAutoTuo":false,"IsATClearScore":false,"LiuShui":0,"ZongYingKui":0,"ZongYingLi":0,"KouLiuShui":0,"XiaZhuQiShu":0}]"
//}