function reveal(options,tag){
    console.log(3333,options,tag)
    var defaults = {  
        animation: 'fadeAndPop', //fade, fadeAndPop, none
        animationspeed: 300, //how fast animtions are
        closeonbackgroundclick: true, //if you click background will modal close?
        dismissmodalclass: 'close-reveal-modal' //the class of a button or element that will close an open modal
    }; 
    
    //Extend dem' options
    var options = $.extend({}, defaults, options); 
    console.log(55555,options)
        var modal = tag,
            topMeasure  = parseInt(modal.css('top')),
            topOffset = modal.height() + topMeasure,
            locked = false,
            modalBG = $('.reveal-modal-bg');

/*-------------Create Modal BG-----------------*/
        if(modalBG.length == 0) {
            modalBG = $('<div class="reveal-modal-bg" />').insertAfter(modal);
        }		    
 
/*-----------Open & Close Animations-------------*/
        //Entrance Animations
        modal.bind('reveal:open', function () {
          modalBG.unbind('click.modalEvent');
            $('.' + options.dismissmodalclass).unbind('click.modalEvent');
            if(!locked) {
                lockModal();
            
                modal.css({'opacity' : 0, 'visibility' : 'visible', 'top': $(document).scrollTop()+topMeasure});
                modalBG.fadeIn(options.animationspeed/2);
                modal.delay(options.animationspeed/2).animate({
                    "opacity" : 1
                }, options.animationspeed,unlockModal());					
                
            }
            modal.unbind('reveal:open');
        }); 	

        //Closing Animation
        modal.bind('reveal:close', function () {
          if(!locked) {
                lockModal();
                
                modalBG.delay(options.animationspeed).fadeOut(options.animationspeed);
                modal.animate({
                    "opacity" : 0
                }, options.animationspeed, function() {
                    modal.css({'opacity' : 1, 'visibility' : 'hidden', 'top' : topMeasure});
                    unlockModal();
                });					
                		
            }
            modal.unbind('reveal:close');
        });     
   
/*----------Open and add Closing Listeners-------------*/
        //Open Modal Immediately
    modal.trigger('reveal:open')
        
        //Close Modal Listeners
        var closeButton = $('.' + options.dismissmodalclass).bind('click.modalEvent', function () {
          modal.trigger('reveal:close')
        });
        
        if(options.closeonbackgroundclick) {
            modalBG.css({"cursor":"pointer"})
            modalBG.bind('click.modalEvent', function () {
              modal.trigger('reveal:close')
            });
        }
        $('body').keyup(function(e) {
            if(e.which===27){ modal.trigger('reveal:close'); } // 27 is the keycode for the Escape key
        });
        
        
/*------------Animations Locks--------------*/
        function unlockModal() { 
            locked = false;
        }
        function lockModal() {
            locked = true;
        }	
        
    
}