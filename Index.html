﻿@using GGKJ.Services;
@using GGKJ.Common;
@inject IRolePower  _RolePower

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <title>三江后台管理系统</title>
    <link href="layuiadmin/layui/css/layui.css" rel="stylesheet" media="all">
    <link href="layuiadmin/style/admin.css" rel="stylesheet" media="all">
    <script src="lib/jquery/dist/jquery.js"></script>
    <script src="lib/bootstrap/dist/js/bootstrap.js"></script>
    <script src="layuiadmin/layui/layui.js"></script>
    <script src="layuiadmin/layui/base.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script>
        layui.use(['util', 'jquery'], function () {
            var util = layui.util, $ = layui.jquery;
            util.fixbar();
            $('.blog-header-menu-icon').on('click', function () {
                var $nav = $('.blog-header-menu-nav');
                $nav.toggle();
            });
        });

        // 静态表格数据
        const tableData = [
            { name: "新疆维吾尔自治区", value: 12 },
            { name: "河南省", value: 10 },
            { name: "云南省", value: 9 },
            { name: "四川省", value: 9 },
            { name: "广东省", value: 9 },
            { name: "山西省", value: 8 },
            { name: "江苏省", value: 8 },
            { name: "山东省", value: 7 },
            { name: "安徽省", value: 6 },
            { name: "浙江省", value: 6 }
        ];

        // 省份名称标准化（去掉“省”、“自治区”等）
        function normalizeProvinceName(name) {
            return name.replace(/省|市|自治区|壮族自治区|回族自治区|维吾尔自治区|特别行政区/g, "");
        }

        // 生成地图数据
        const mapData = tableData.map(item => ({
            name: normalizeProvinceName(item.name),
            value: item.value
        }));

        // 初始化地图
        // layui.use(['jquery'], function () {
        //     var $ = layui.jquery;
        //     // 加载地图数据
        //     $.get('layuiadmin/map/china.json', function (geoJson) {
        //         echarts.registerMap('china', geoJson);
        //         var myChart = echarts.init(document.getElementById('chinaMap'));
        //         var option = {
        //             tooltip: {
        //                 trigger: 'item',
        //                 formatter: '{b}: {c}人'
        //             },
        //             visualMap: {
        //                 min: 0,
        //                 max: 100,
        //                 left: 'left',
        //                 top: 'bottom',
        //                 text: ['高','低'],
        //                 inRange: {
        //                     color: ['#e0ffff', '#006edd']
        //                 },
        //                 calculable: true
        //             },
        //             series: [{
        //                 name: '人数',
        //                 type: 'map',
        //                 map: 'china',
        //                 roam: true,
        //                 data: mapData
        //             }]
        //         };
        //         myChart.setOption(option);
        //     });
        // });
    </script>
</head>
<body class="layui-layout-body">
    <div id="LAY_app">
        <div class="layui-layout layui-layout-admin">
            <div class="layui-header">
                <!-- 头部区域 -->
                <ul class="layui-nav layui-layout-left">
                    <li class="layui-nav-item layadmin-flexible" lay-unselect>
                        <a href="javascript:;" layadmin-event="flexible" title="侧边伸缩">
                            <i class="layui-icon layui-icon-shrink-right" id="LAY_app_flexible"></i>
                        </a>
                    </li>
                    <li class="layui-nav-item layui-hide-xs" lay-unselect>
                        <a href="/Home/Index" title="前台">
                            <i class="layui-icon layui-icon-website"></i>
                        </a>
                    </li>
                    <li class="layui-nav-item" lay-unselect>
                        <a href="javascript:;" layadmin-event="refresh" title="刷新">
                            <i class="layui-icon layui-icon-refresh-3"></i>
                        </a>
                    </li>
                    <li class="layui-nav-item" lay-unselect>
                        <a href="javascript:;">
                            <cite>777</cite>
                        </a>
                        <dl class="layui-nav-child">

                            <dd><a lay-href="/Admin/Home/MyInfo/">基本资料</a></dd>
                            <dd><a lay-href="/Admin/Home/PassEdit">修改密码</a></dd>
                            <hr\>
                                <dd style="text-align: center;"><a href="@Url.Action("LoginOut","Home")">退出</a></dd>
                        </dl>
                    </li>
                    <li class="layui-nav-item layui-hide-xs" lay-unselect>
                        <a href="javascript:;" layadmin-event="theme">
                            <i class="layui-icon layui-icon-theme"></i>
                        </a>
                    </li>
                    <li class="layui-nav-item layui-hide-xs" lay-unselect>
                        <a href="javascript:;" layadmin-event="note">
                            <i class="layui-icon layui-icon-note"></i>
                        </a>
                    </li>
                    <li class="layui-nav-item layui-hide-xs" lay-unselect>
                        <a href="javascript:;" layadmin-event="fullscreen">
                            <i class="layui-icon layui-icon-screen-full"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- 侧边菜单 -->
            <div class="layui-side layui-side-menu">
                <div class="layui-side-scroll">
                    <div class="layui-logo">
                        <a lay-href="/Admin/Users/<USER>">
                            <img src="layuiadmin/style/res/logo.png" style="width:40px;float: left;padding-top: 5px;" />
                            <span>三江后台管理</span>
                        </a>
                    </div>
                    <ul class="layui-nav layui-nav-tree" lay-shrink="all" id="LAY-system-side-menu" lay-filter="layadmin-system-side-menu">
                            <li data-name="Member" class="layui-nav-item layui-nav-itemed">
                                <a href="javascript:;" lay-tips="账户管理" lay-direction="1">
                                    <i class="layui-icon layui-icon-user"></i>
                                    <cite>账户管理</cite>
                                </a>
                                <dl class="layui-nav-child">
                                    <dd data-name="admin">
                                        <a lay-href="@Url.Action("AdminList", "Admin")">管理员</a>
                                    </dd>
                                    <dd data-name="admin">
                                        <a lay-href="@Url.Action("UserInfoList2", "UserInfoManage")">员工账户</a>
                                    </dd>
                                    <dd data-name="user">
                                        <a lay-href="@Url.Action("UserInfoList", "UserInfoManage")">外部账户</a>
                                    </dd>
                                    <dd data-name="admin">
                                        <a lay-href="@Url.Action("QuanXianList", "UserInfoManage")">权限设置</a>
                                    </dd>
                                    <dd data-name="admin">
                                        <a lay-href="@Url.Action("PositionList", "Admin")">职位管理</a>
                                    </dd>
                                    <dd data-name="admin">
                                        <a lay-href="@Url.Action("DepartmentList", "Admin")">部门管理</a>
                                    </dd>
                                    <dd data-name="admin">
                                        <a lay-href="@Url.Action("CompanyList", "Admin")">公司管理</a>
                                    </dd>
                                    <dd data-name="admin">
                                        <a lay-href="@Url.Action("Message1List", "System")">权限申请</a>
                                    </dd>
                                </dl>
                            </li>
                        <li data-name="BiaoDan" class="layui-nav-item">
                            <a href="javascript:;" lay-tips="文档管理" lay-direction="2">
                                <i class="layui-icon layui-icon-user"></i>
                                <cite>文档管理</cite>
                            </a>
                            <dl class="layui-nav-child">
                                    <dd data-name="user">
                                        <a lay-href="@Url.Action("Category", "Category", new { Id = 6 })">产品系统</a>
                                    </dd>
                                    <dd data-name="user">
                                        <a lay-href="@Url.Action("Category", "Category", new { Id = 7 })">产品系列</a>
                                    </dd>
                                    <dd data-name="product">
                                        <a lay-href="@Url.Action("ProductList", "Product")">产品管理</a>
                                    </dd>
                                    <dd data-name="user">
                                        <a lay-href="@Url.Action("Category1", "Category", new { Id = 8 })">资料检索标签</a>
                                    </dd>
                                    <dd data-name="category">
                                        <a lay-href="@Url.Action("Category", "Category", new { Id = 5 })">资料分类</a>
                                    </dd>
                                @*<dd data-name="neirong">
                                        <a lay-href="@Url.Action("MaterialInfoList", "Material")">资料管理</a>
                                    </dd>*@
                                <dd data-name="neirong">
                                    <a lay-href="@Url.Action("MaterialInfoList3", "Material")">资料管理</a>
                                </dd>
                                <dd data-name="neirong">
                                    <a lay-href="@Url.Action("MaterialInfoList", "Material")">资料待审核</a>
                                </dd>
                                @*<dd data-name="qudao">
                                        <a lay-href="@Url.Action("Category1", "Category",new { Id = 3})">渠道管理</a>
                                    </dd>
                                    <dd data-name="biaoqian">
                                        <a lay-href="@Url.Action("Category1", "Category",new { Id = 4})">标签管理</a>
                                    </dd>*@
                                @*<dd data-name="user" class="layui-this">
                                        <a lay-href="@Url.Action("MaterialInfoList", "Material",new { Id = 1})">企业宣传类</a>
                                    </dd>
                                    <dd data-name="user">
                                        <a lay-href="@Url.Action("MaterialInfoList", "Material",new { Id = 2})">市场推广类</a>
                                    </dd>
                                    <dd data-name="user">
                                        <a lay-href="@Url.Action("MaterialInfoList", "Material",new { Id = 3})">产品推广类</a>
                                    </dd>
                                    <dd data-name="user">
                                        <a lay-href="@Url.Action("MaterialInfoList", "Material",new { Id = 4})">产品安装类</a>
                                    </dd>*@
                            </dl>
                        </li>
                        @if (ViewBag.AdminType == 1)
                        {
                            <li data-name="Member" class="layui-nav-item">
                                <a href="javascript:;" lay-tips="公告管理" lay-direction="3">
                                    <i class="layui-icon layui-icon-user"></i>
                                    <cite>公告管理</cite>
                                </a>
                                <dl class="layui-nav-child">
                                    @*<dd data-name="gonggao">
                                            <a lay-href="@Url.Action("ArticleList", "Article",new { Id = 1})">公告分类</a>
                                        </dd>*@
                                    <dd data-name="gonggao">
                                        <a lay-href="@Url.Action("ArticleList", "Article", new { Id = 1 })">公告内容</a>
                                    </dd>
                                </dl>
                            </li>
                        }

                        @if (ViewBag.AdminType == 1)
                        {
                            <li data-name="Member" class="layui-nav-item">
                                <a href="javascript:;" lay-tips="表单管理" lay-direction="4">
                                    <i class="layui-icon layui-icon-user"></i>
                                    <cite>表单管理</cite>
                                </a>
                                <dl class="layui-nav-child">
                                    <dd data-name="role">
                                        <a lay-href="@Url.Action("BiaoDanInfoList1", "BiaoDan")">表单</a>
                                    </dd>

                                </dl>
                            </li>
                        }

                        @if (ViewBag.AdminType == 1)
                        {
                            <li data-name="Member" class="layui-nav-item">
                                <a href="javascript:;" lay-tips="用户访问管理" lay-direction="5">
                                    <i class="layui-icon layui-icon-user"></i>
                                    <cite>用户访问管理</cite>
                                </a>
                                <dl class="layui-nav-child">
                                    <dd data-name="user">
                                        <a lay-href="@Url.Action("LoginLogList", "System")">访问记录</a>
                                    </dd>
                                    <dd data-name="user">
                                        <a lay-href="@Url.Action("CommentInfoList", "Material")">评论管理</a>
                                    </dd>
                                </dl>
                            </li>
                        }

                        @if (ViewBag.AdminType == 1)
                        {
                            <li data-name="Member" class="layui-nav-item">
                                <a href="javascript:;" lay-tips="系统管理" lay-direction="6">
                                    <i class="layui-icon layui-icon-user"></i>
                                    <cite>系统管理</cite>
                                </a>
                                <dl class="layui-nav-child">
                                    <dd data-name="gonggao">
                                        <a lay-href="@Url.Action("BannerList", "ConfigManage", new { Id = 1 })">Banner管理</a>
                                    </dd>
                                    <dd data-name="gonggao">
                                        <a lay-href="@Url.Action("ArticleSingle", "Article", new { Id = 1 })">隐私政策</a>
                                    </dd>
                                    <dd data-name="gonggao">
                                        <a lay-href="">使用帮助</a>
                                    </dd>
                                    <dd data-name="gonggao">
                                        <a lay-href="">投诉建议</a>
                                    </dd>
                                </dl>
                            </li>
                        }
                    </ul>
                </div>
            </div>

            <!-- 页面标签 -->
            <div class="layadmin-pagetabs" id="LAY_app_tabs">
                <div class="layui-icon layadmin-tabs-control layui-icon-prev" layadmin-event="leftPage"></div>
                <div class="layui-icon layadmin-tabs-control layui-icon-next" layadmin-event="rightPage"></div>
                <div class="layui-icon layadmin-tabs-control layui-icon-down">
                    <ul class="layui-nav layadmin-tabs-select" lay-filter="layadmin-pagetabs-nav">
                        <li class="layui-nav-item" lay-unselect>
                            <a href="javascript:;"></a>
                            <dl class="layui-nav-child layui-anim-fadein">
                                <dd layadmin-event="closeThisTabs"><a href="javascript:;">关闭当前标签页</a></dd>
                                <dd layadmin-event="closeOtherTabs"><a href="javascript:;">关闭其它标签页</a></dd>
                                <dd layadmin-event="closeAllTabs"><a href="javascript:;">关闭全部标签页</a></dd>
                            </dl>
                        </li>
                    </ul>
                </div>
                <div class="layui-tab" lay-unauto lay-allowClose="true" lay-filter="layadmin-layout-tabs">
                    <ul class="layui-tab-title" id="LAY_app_tabsheader">
                        <li lay-id="home/console.html" lay-attr="home/console.html" class="layui-this"><i class="layui-icon layui-icon-home"></i></li>
                    </ul>
                </div>
            </div>

            <div class="layui-body" id="LAY_app_body">
                <div class="layadmin-tabsbody-item layui-show">
                    <iframe src="Base.html" frameborder="0" class="layadmin-iframe"></iframe>
                </div>
            </div>

            <!-- 辅助元素，一般用于移动设备下遮罩 -->
            <div class="layadmin-body-shade" layadmin-event="shade"></div>
        </div>
    </div>

    <div class="layui-footer">
        <div class="blog-footer-main">
        </div>
    </div>

    <script>
        layui.config({
            base: 'layuiadmin/' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use('index');
        function CloseTab() {
            $("#LAY_app_tabs").children(".layui-this").children(".layui-tab-close").trigger("click");
        }
    </script>
    <!-- 在合适位置添加地图容器 -->
    <div id="chinaMap" style="width: 600px; height: 400px; float: left;"></div>
</body>
</html>