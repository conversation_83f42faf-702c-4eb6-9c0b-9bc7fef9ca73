/****** 整站通用 ******/
@charset "UTF-8";

*{
	padding: 0;
	margin: 0;
	box-sizing: border-box;
}
html {
	-webkit-text-size-adjust: none;
	-moz-text-size-adjust: none;
	-ms-text-size-adjust: none;
	text-size-adjust: none;
	/*解决chrome浏览器下字体不能小于12px*/
	-webkit-font-smoothing:antialiased;
	-webkit-tap-highlight-color: transparent;
}
body {
	overflow-x: hidden;
	font-size: 14px;
	color: #333;
	font-family: "Microsoft YaHei",-apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "PingFang SC", "Hiragino Sans GB", sans-serif;
}
html {
	zoom: 1;
}
html * {
	outline: 0;
	zoom: 1;
}
html button::-moz-focus-inner {
	border-color: transparent !important;
}
/*设置margin和padding为0*/
body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td {
	margin: 0;
	padding: 0;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}
fieldset,a,img,input,select,button,textarea {
	border: 0;
	background:none;
	-webkit-appearance: none;
	border-radius:0;
}
address,caption,cite,code,dfn,em,th,var，i {
	font-style: normal;
	font-weight: normal;
}
a {
	outline: none;
	text-decoration: none;
	color: inherit;
}
a:hover {
	text-decoration: none;
}
li {
	list-style: none;

}
caption,th {
	text-align: left;
}

h1,h2,h3,h4,h5,h6 {
	font-size: 100%;
	font-weight: normal;
}
q:before,q:after {
	content: '';
}
em,i {
	font-style: normal;
}
input[type="submit"],input[type="reset"],input[type="button"],input[type=date],button,select,input[type=text] {
	/*去掉苹果的默认UI来渲染按钮 、解决部分手机浏览器对border：none无效办法*/
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	/*去掉微信端input-text在ios轻快下的点击出先黑色半透明背景*/
	tap-highlight-color: transparent !important;
}
a,input[type=text],select,input[type=password],textarea {
	/*去掉微信端input-text在ios轻快下的点击出先黑色半透明背景*/
	tap-highlight-color: transparent !important;
}
input::-webkit-input-placeholder,textarea::-webkit-input-placeholder 
{
	font-family:"Microsoft YaHei",-apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "PingFang SC", "Hiragino Sans GB", sans-serif;
}
li,em,i{list-style:none;font-style: normal;}
img{
	width: auto;
	max-width: 100%;
	display: block;
}

.container{
	width: 100%;
	margin: auto;
}

.taotelaxin{
	margin: 20px;
	padding: 20px;
	background: #fff;

	.flex{
		display: flex;
		justify-content: space-between;
	}
	li{
		font-size: 0;
		border-top: 1px solid #eee;

		span{
			overflow: hidden;
			text-overflow: ellipsis;
			-o-text-overflow: ellipsis;
			white-space: nowrap;
			display: inline-block;
			width: 50%;
			line-height: 50px;
			font-size: 14px;
			border-left: 1px solid #eee;
			text-align: center;

			&:first-child{
				font-weight: bold;
			}
			&:nth-child(2n){
				border-right: 0;
			}
		}
		&:first-child{
			border-top: 0;
		}
	}
	ul{
		width: 100%;
		margin-top: 20px;
		border: 1px solid #eee;
		border-bottom: 0;
		border-left: 0;

		&:last-child{
			border-bottom: 1px solid #eee;
		}
	}
	.column-th{
		margin-top: 0;
	}
	.set-line{
		display: flex;
		align-items: center;
		
		
		.word-title{
			padding-bottom: 10px;
			font-size: 20px;
			color: #666;
			font-weight: bold;
		}
		.word{
			padding-left: 10px;
		}
		.logo{
			width: 80px;
		}
	}
	.column{
		li{
			span{
				width: 25%;
			}
		}
		.column-th{
			span{
				font-weight: bold;
			}
		}
		ul{
			margin: 0;
		}
	}
	.confirm{
		.mores{
			display: block;
			width: 120px;
			max-width: 100%;
			line-height: 50px;
			text-align: center;
			color: #fff;
			background: #21c1d6;
			box-shadow: 0 2px 1px rgba(0, 0, 0, .45);
		}
	}
	.item-list01{
		width: 20%;
		padding: 20px 2.5% 20px 20px;
		border-right: 1px solid #eee;
	}
	.item-list02{
		width: 70%;
		padding-left: 2.5%;
		padding-right: 2.5%;
		border-right: 1px solid #eee;
	}
	.item-list03{
		width: 10%;
		padding-left: 2.5%;
	}
}

@media screen and(max-width: 1400px){
	.container{
		width: 1200px;
	}
	.taotelaxin{
		.set-line{
			.word-title{
				padding-bottom: 5px;
				font-size: 16px;
			}
		}
		.item-list01{
			padding-left: 10px;
		}
	}
}
@media screen and(max-width: 1200px){
	.container{
		width: 992px;
	}
	.taotelaxin{
		li{
			span{
				font-size: 12px;
			}
		}
		.set-line{
			.word-title{
				padding-bottom: 0;
				font-size: 14px;
			}
			.word-number{
				font-size: 12px;
			}
		}
		.confirm{
			.mores{
				font-size: 12px;
			}
		}
	}
}
@media screen and(max-width: 992px){
	.container{
		width: 768px;
	}
	.taotelaxin{
		ul{
			margin-top: 10px;
		}
		.set-line{
			flex-wrap: wrap;
			
			.word-title{
				padding-bottom: 5px;
			}
			.word{
				padding-left: 0;
			}
		}
		.set{
			li{
				span{
					display: block;
					width: 100%;
					border-bottom: 1px solid #eee;

					&:last-child{
						border-bottom: 0;
					}
				}
			}
		}
		.confirm{
			.mores{
				width: 80px;
			}
		}
		.item-list02{
			width: 80%;
			padding-right: 0;
			border: 0;
		}
		.item-list03{
			width: 100%;
			padding-top: 20px;
			border-top: 1px solid #eee;
		}
		.flex{
			flex-wrap: wrap;
		}
	}
}
@media screen and (max-width: 768px){
	.container{
		width: 100%;
	}
	.taotelaxin{
		li{
			span{
				font-size: .6rem;
				line-height: 40px;
			}
		}
		.set-line{
			.word-title{
				padding-bottom: 10px;
				font-size: 1rem;
				
			}
			.word{
				padding-left: 5%;
				
			}
		}
		.confirm{
			.mores{
				line-height: 40px;
			}
		}
		.item-list01{
			width: 100%;
			padding: 20px 0;
			border-right: 0;
			border-bottom: 1px solid #eee;
		}
		.item-list02{
			width: 100%;
			margin-top: 20px;
			padding: 0;
		}
		.item-list03{
			margin-top: 20px;
			padding-left: 0;		}
	}
}