# Changelog

## 2.0.5 (Dec 1, 2018)

- Fixed the issue of trigger change event twice (#68).

## 2.0.4 (Jun 1, 2018)

- Update districts.

## 2.0.3 (Mar 1, 2018)

- Update districts.

## 2.0.2 (Dec 1, 2017)

- Trigger change event once district changed.
- Support to load in node environment.
- Update districts.

## 2.0.1 (Sep 1, 2017)

- Update districts.

## 2.0.0 (Jun 1, 2017)

- Update districts.
- Use `window.jQuery` instead of `window.$` for browser side usage.
- Change the `main` field value from `dist/distpicker.js` (UMD) to `dist/distpicker.common.js` (CommonJS).
- Added `module` and `browser` fields to `package.json`.

## 2.0.0-rc (Mar 4, 2017)

- Update districts.

## 2.0.0-beta.2 (Dec 8, 2016)

- Build the source code with [Rollup](https://github.com/rollup/rollup).

## 2.0.0-beta.1 (Oct 15, 2016)

- Fixed the issue of restoring saved districts by code (#19).

## 2.0.0-alpha.2 (Sep 6, 2016)

- Rename `autoSelect` option to `autoselect`.
- Subdivide optional values for `autoselect` option (#13).
- Fixed the wrong value of the `<select>` elements (#18).

## 2.0.0-alpha.1 (Aug 11, 2016)

- Merge the `distpicker.data.js` file to the `distpicker.js` file.
- Added a new option: `valueType`.
- Added a new method: `getDistricts`.

## 1.0.4 (Jun 1, 2016)

- Update districts data.

## 1.0.3 (Mar 3, 2016)

- Update districts data.

## 1.0.2 (Dec 26, 2015)

- Update lots of districts.

## 1.0.1 (Sep 6, 2015)

- Added new cities of Xizang (Tibet).

## 1.0.0 (Aug 27, 2015)

- Added new districts of Sanya city.
- Optimized code style.

## 0.2.1 (Dec 26, 2014)

- Enable to change the global default options.
- Fix placeholder bug.
- Improve code.

## 0.2.0 (Dec 20, 2014)

- Add new options: "autoSelect", "placeholder".
- Add new methods: "reset", "destroy".
- Use "data-distpicker" attribute for initializing automatically instead of "distpicker" attribute.

## 0.1.3 (Aug 9, 2014)

- Improve

## 0.1.2 (Apr 8, 2014)

- Fix a type error bug when initialize a city without districts.

## 0.1.1 (Feb 21, 2014)

- Fix bug: The default options will be changed when use multiple times in one page.

## 0.1.0 (Jan 25, 2014)
