﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title></title>
    <link rel="stylesheet" href="./layui/css/layui.css">
    <style>
        body {
            margin: 0;
            padding: 15px;
            background-color: #f2f2f2;
        }
        .layui-card-header {
            font-size: 16px;
            font-weight: bold;
        }
        .data-overview {
            text-align: center;
            padding: 20px 0;
        }
        .data-value {
            font-size: 28px;
            color: #333;
            margin-bottom: 5px;
        }
        .data-compare {
            font-size: 14px;
            color: #999;
        }
        .layui-col-md2, .layui-col-md10 {
            padding: 5px;
            
        }
        .layui-tab-brief > .layui-tab-title .layui-this {
            color: #009688;
        }
        .layui-tab-brief > .layui-tab-title .layui-this:after {
            border-bottom: 2px solid #009688;
        }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-tab layui-tab-brief">
                    <ul class="layui-tab-title">
                        <li class="layui-this">用户数据分析</li>
                        <li>资料数据分析</li>
                        <!-- Add more navigation items if needed -->
                    </ul>
                </div>
            </div>

            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="layui-form layui-form-pane">
                            <div class="layui-form-item" style="align-items: center;display: flex;">
                                <label class="layui-form-label">统计时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input" id="test-laydate-range-date" placeholder="请选择日期">
                                </div>
                                <div class="layui-input-inline" style="display: flex;">
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="realtime-btn">实时</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="1day-btn">1天</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="7day-btn">7天</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="30day-btn">30天</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="day-btn">日</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="week-btn">周</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="month-btn">月</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="custom-btn">自定义</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="prev-btn"><i class="layui-icon layui-icon-left"></i></button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="next-btn"><i class="layui-icon layui-icon-right"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md12">
                <div class="layui-row layui-col-space15">
                    
                    <!-- 折线图区域 -->
                    <div >
                        <div class="layui-card">
                            <div class="layui-card-header">用户趋势图</div>
                            <div class="layui-card-body">
                                <div id="user-chart" style="height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md12">
                <div class="layui-row layui-col-space15">
                    <!-- 用户总览 -->
                    <div class="layui-col-md2">
                        <div class="layui-card">
                            <div class="layui-card-header">用户总览</div>
                            <div class="layui-card-body data-overview">
                                <div class="data-value" id="total-users">--</div>
                                <div class="data-compare">较前1周: <span id="total-users-compare">--</span></div>
                            </div>
                        </div>
                    </div>
                    <!-- 老用户数 -->
                    <div class="layui-col-md2">
                        <div class="layui-card">
                            <div class="layui-card-header">老用户数</div>
                            <div class="layui-card-body data-overview">
                                <div class="data-value" id="old-users">--</div>
                                <div class="data-compare">较前1周: <span id="old-users-compare">--</span></div>
                            </div>
                        </div>
                    </div>
                    <!-- 新用户数 -->
                    <div class="layui-col-md2">
                        <div class="layui-card">
                            <div class="layui-card-header">新用户数</div>
                            <div class="layui-card-body data-overview">
                                <div class="data-value" id="new-users">--</div>
                                <div class="data-compare">较前1周: <span id="new-users-compare">--</span></div>
                            </div>
                        </div>
                    </div>
                    <!-- 游客用户数量 -->
                    <div class="layui-col-md2">
                        <div class="layui-card">
                            <div class="layui-card-header">游客用户数量</div>
                            <div class="layui-card-body data-overview">
                                <div class="data-value" id="visitor-users">--</div>
                                <div class="data-compare">较前1周: <span id="visitor-users-compare">--</span></div>
                            </div>
                        </div>
                    </div>
                    <!-- 游客注册数量 -->
                    <div class="layui-col-md2">
                        <div class="layui-card">
                            <div class="layui-card-header">游客注册数量</div>
                            <div class="layui-card-body data-overview">
                                <div class="data-value" id="registered-visitors">--</div>
                                <div class="data-compare">较前1周: <span id="registered-visitors-compare">--</span></div>
                            </div>
                        </div>
                    </div>
                    <!-- 经销商 -->
                    <div class="layui-col-md2">
                        <div class="layui-card">
                            <div class="layui-card-header">经销商</div>
                            <div class="layui-card-body data-overview">
                                <div class="data-value" id="dealers">--</div>
                                <div class="data-compare">较前1周: <span id="dealers-compare">--</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./layui/layui.js"></script>
    <!-- Assuming echarts.min.js is in a 'lib' folder, adjust path if necessary -->
    <script src="./lib/echarts.min.js"></script>
    <script>
        layui.use(['form', 'element', 'laydate'], function(){
            var form = layui.form;
            var element = layui.element;
            var laydate = layui.laydate;

            // Date picker for "统计时间"
            laydate.render({
                elem: '#test-laydate-range-date'
                ,range: true
            });

            // Initialize ECharts instance
            var userChart = echarts.init(document.getElementById('user-chart'));

            // Sample ECharts option (replace with actual data later)
            var option = {
                title: {
                    text: '用户趋势'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['系列1', '系列2', '系列3', '系列4', '系列5'] // Example series names
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] // Days of the week
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '系列1',
                        type: 'line',
                        data: [1600, 1800, 1700, 1800, 2250, 2550, 2580] // Sample data for series 1
                    },
                    {
                        name: '系列2',
                        type: 'line',
                        data: [800, 900, 800, 900, 1000, 1200, 1220] // Sample data for series 2
                    },
                    {
                        name: '系列3',
                        type: 'line',
                        data: [500, 550, 500, 550, 600, 900, 950] // Sample data for series 3
                    },
                    {
                        name: '系列4',
                        type: 'line',
                        data: [350, 300, 300, 400, 450, 550, 520] // Sample data for series 4
                    },
                    {
                        name: '系列5',
                        type: 'line',
                        data: [100, 150, 100, 150, 100, 250, 220] // Sample data for series 5
                    }
                ]
            };
            userChart.setOption(option);

            // Placeholder for data fetching and update logic
            function updateDashboardData(startDate, endDate) {
                // Here you would typically make an AJAX request to your backend
                // to fetch actual data based on startDate and endDate.
                // For now, we'll just use placeholder values.
                document.getElementById('total-users').innerText = '12345';
                document.getElementById('total-users-compare').innerText = '+5%';
                document.getElementById('old-users').innerText = '8000';
                document.getElementById('old-users-compare').innerText = '+2%';
                document.getElementById('new-users').innerText = '4345';
                document.getElementById('new-users-compare').innerText = '+10%';
                document.getElementById('visitor-users').innerText = '2000';
                document.getElementById('visitor-users-compare').innerText = '-3%';
                document.getElementById('registered-visitors').innerText = '1500';
                document.getElementById('registered-visitors-compare').innerText = '+7%';
                document.getElementById('dealers').innerText = '500';
                document.getElementById('dealers-compare').innerText = '+1%';

                // Update chart data
                // userChart.setOption({
                //     series: [...]
                // });
            }

            // Initial data load
            updateDashboardData(null, null);

            // Event listeners for time selection buttons
            document.getElementById('realtime-btn').onclick = function() {
                // Logic for "实时"
                console.log('实时 clicked');
                updateDashboardData('实时', null);
            };
            document.getElementById('1day-btn').onclick = function() {
                // Logic for "1天"
                console.log('1天 clicked');
                updateDashboardData('1天', null);
            };
            document.getElementById('7day-btn').onclick = function() {
                // Logic for "7天"
                console.log('7天 clicked');
                updateDashboardData('7天', null);
            };
            document.getElementById('30day-btn').onclick = function() {
                // Logic for "30天"
                console.log('30天 clicked');
                updateDashboardData('30天', null);
            };
            document.getElementById('day-btn').onclick = function() {
                // Logic for "日"
                console.log('日 clicked');
                updateDashboardData('日', null);
            };
            document.getElementById('week-btn').onclick = function() {
                // Logic for "周"
                console.log('周 clicked');
                updateDashboardData('周', null);
            };
            document.getElementById('month-btn').onclick = function() {
                // Logic for "月"
                console.log('月 clicked');
                updateDashboardData('月', null);
            };
            document.getElementById('custom-btn').onclick = function() {
                // Logic for "自定义" - laydate handles this
                console.log('自定义 clicked');
            };
            document.getElementById('prev-btn').onclick = function() {
                // Logic for previous period
                console.log('上一周期 clicked');
            };
            document.getElementById('next-btn').onclick = function() {
                // Logic for next period
                console.log('下一周期 clicked');
            };
        });
    </script>
</body>
</html>

