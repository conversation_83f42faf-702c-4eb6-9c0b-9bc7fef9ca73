﻿<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title></title>
    <link rel="stylesheet" href="./layui/css/layui.css">
    <style>
        body {
            margin: 0;
            padding: 15px;
            background-color: #f2f2f2;
        }

        .layui-card-header {
            font-size: 16px;
            font-weight: bold;
        }

        .data-overview {
            text-align: center;
            padding: 20px 0;
        }

        .data-value {
            font-size: 28px;
            color: #333;
            margin-bottom: 5px;
        }

        .data-compare {
            font-size: 14px;
            color: #999;
        }

        .layui-col-md2,
        .layui-col-md10 {
            padding: 5px;

        }

        .layui-tab-brief>.layui-tab-title .layui-this {
            color: #009688;
        }

        .layui-tab-brief>.layui-tab-title .layui-this:after {
            border-bottom: 2px solid #009688;
        }
    </style>
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-tab layui-tab-brief">
                    <ul class="layui-tab-title">
                        <li class="layui-this" id="user-tab">用户数据分析</li>
                        <li id="profile-tab">资料数据分析</li>
                    </ul>
                </div>
            </div>

            <div class="layui-col-md12">
                <div id="user-analysis-area">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="layui-form layui-form-pane">
                                <div  style="align-items: center;display: flex;width: 100%;">
                                    <label class="layui-form-label" style="width: 140px;">统计时间</label>
                                    <div class="layui-input-inline">
                                        <input type="text" class="layui-input" style="width:200px;" id="test-laydate-range-date"
                                            placeholder="请选择日期" disabled>
                                    </div>
                                    <div class="layui-input-inline" style="display: flex;justify-content: flex-end;width: 100%;">
                                        <button class="layui-btn layui-btn-primary layui-btn-sm"
                                            id="realtime-btn">实时</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="1day-btn">1天</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="7day-btn">7天</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="30day-btn">30天</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="day-btn">日</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="week-btn">周</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="month-btn">月</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" style="position: relative;"
                                            id="custom-btn">自定义
                                            <div style="width:100%;height:100%;position:absolute;background: red;top: 0;left: 0;opacity:0;"
                                                id="hidden-laydate-input">12312</div>
                                        </button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="prev-btn"><i
                                                class="layui-icon layui-icon-left"></i></button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="next-btn"><i
                                                class="layui-icon layui-icon-right"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-col-md12">
                        <div class="layui-row layui-col-space15">

                            <!-- 折线图区域 -->
                            <div>
                                <div class="layui-card">
                                    <div class="layui-card-header">用户趋势图</div>
                                    <div class="layui-card-body">
                                        <div id="user-chart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-col-md12" style="margin-top: 15px;">
                        <div class="layui-row layui-col-space15">
                            <!-- 用户总览 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">用户总览</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="total-users">--</div>
                                        <div class="data-compare">较前1周: <span id="total-users-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 老用户数 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">老用户数</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="old-users">--</div>
                                        <div class="data-compare">较前1周: <span id="old-users-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 新用户数 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">新用户数</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="new-users">--</div>
                                        <div class="data-compare">较前1周: <span id="new-users-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 游客用户数量 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">游客用户数量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="visitor-users">--</div>
                                        <div class="data-compare">较前1周: <span id="visitor-users-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 游客注册数量 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">游客注册数量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="registered-visitors">--</div>
                                        <div class="data-compare">较前1周: <span id="registered-visitors-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 经销商 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">经销商</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="dealers">--</div>
                                        <div class="data-compare">较前1周: <span id="dealers-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 平台用户属性分析 -->
                    <!-- <div class="layui-col-md12">
                        <div class="layui-tab layui-tab-brief" style="margin-top: 15px;">
                            <ul class="layui-tab-title">
                                <li class="layui-this">平台用户属性分析</li>
                            </ul>
                        </div>
                    </div> -->

                    <div class="layui-col-md12" style="margin-top: 15px;">
                        <div class="layui-row ">
                            <!-- 人员类型分析 -->
                            <div class="layui-col-md6">
                                <div class="layui-card">
                                    <div class="layui-card-header">人员类型分析</div>
                                    <div class="layui-card-body">
                                        <div id="user-type-chart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- 访客来源分析 -->
                            <div class="layui-col-md6">
                                <div class="layui-card">
                                    <div class="layui-card-header">访客来源分析</div>
                                    <div class="layui-card-body">
                                        <div id="visitor-source-chart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 地区分布 -->
                    <div class="layui-col-md12" style="margin-top: 15px;">
                        <div class="layui-card">
                            <div class="layui-card-header">地区分布</div>
                            <div class="layui-card-body layui-row">
                                <div class="layui-col-md6">
                                    <div id="area-map-chart" style="height: 400px;"></div>
                                </div>
                                <div class="layui-col-md6">
                                    <table class="layui-table" lay-even lay-skin="row" id="area-distribution-table"
                                        lay-filter="area-distribution-table"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="profile-analysis-area">

                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="layui-form layui-form-pane">
                                <div  style="align-items: center;display: flex;width: 100%;">
                                    <label class="layui-form-label" style="width: 140px;">统计时间</label>
                                    <div class="layui-input-inline">
                                        <input type="text" class="layui-input" style="width:200px;" id="profile-laydate-range-date"
                                            placeholder="请选择日期" disabled>
                                    </div>
                                    <div class="layui-input-inline" style="display: flex;justify-content: flex-end;width: 100%;">
                                        <button class="layui-btn layui-btn-normal layui-btn-sm"
                                            id="profile-realtime-btn">实时</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="profile-1day-btn">1天</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="profile-7day-btn">7天</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="profile-30day-btn">30天</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="profile-day-btn">日</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="profile-week-btn">周</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="profile-month-btn">月</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" style="position: relative;"
                                            id="profile-custom-btn">自定义
                                            <div style="width:100%;height:100%;position:absolute;background: red;top: 0;left: 0;opacity:0;"
                                                id="profile-hidden-laydate-input">12312</div>
                                        </button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="profile-prev-btn"><i
                                                class="layui-icon layui-icon-left"></i></button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="profile-next-btn"><i
                                                class="layui-icon layui-icon-right"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-col-md12">
                        <div class="layui-row layui-col-space15">

                            <!-- 资料趋势图区域 -->
                            <div>
                                <div class="layui-card">
                                    <div class="layui-card-header">资料趋势图</div>
                                    <div class="layui-card-body">
                                        <div id="profile-chart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-col-md12" style="margin-top: 15px;">
                        <div class="layui-row layui-col-space15">
                            <!-- 资料总览 -->
                            <div class="layui-col-md3">
                                <div class="layui-card">
                                    <div class="layui-card-header">资料总览</div>
                                    <div class="layui-card-body data-overview">
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                                            <div style="text-align: center;">
                                                <div style="font-size: 14px; color: #666;">资料数</div>
                                                <div class="data-value" id="profile-total-docs">--</div>
                                                <div class="data-compare">较前1周: <span id="profile-total-docs-compare">--</span></div>
                                            </div>
                                            <div style="text-align: center;">
                                                <div style="font-size: 14px; color: #666;">访问量</div>
                                                <div class="data-value" id="profile-visits">--</div>
                                                <div class="data-compare">较前1周: <span id="profile-visits-compare">--</span></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 浏览量 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">浏览量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="profile-views">--</div>
                                        <div class="data-compare">较前1周: <span id="profile-views-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 下载量 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">下载量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="profile-downloads">--</div>
                                        <div class="data-compare">较前1周: <span id="profile-downloads-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 收藏量 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">收藏量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="profile-favorites">--</div>
                                        <div class="data-compare">较前1周: <span id="profile-favorites-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 上传量 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">上传量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="profile-uploads">--</div>
                                        <div class="data-compare">较前1周: <span id="profile-uploads-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 资料分类浏览量统计 -->
                    <div class="layui-col-md12" style="margin-top: 15px;">
                        <div class="layui-row layui-col-space15">
                            <!-- 企业宣传资料浏览量 -->
                            <div class="layui-col-md3">
                                <div class="layui-card">
                                    <div class="layui-card-header">企业宣传资料浏览量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="profile-company-views">--</div>
                                        <div class="data-compare">较前1周: <span id="profile-company-views-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 国内产品资料浏览量 -->
                            <div class="layui-col-md3">
                                <div class="layui-card">
                                    <div class="layui-card-header">国内产品资料浏览量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="profile-domestic-product-views">--</div>
                                        <div class="data-compare">较前1周: <span id="profile-domestic-product-views-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 国内解决方案服务浏览量 -->
                            <div class="layui-col-md3">
                                <div class="layui-card">
                                    <div class="layui-card-header">国内解决方案服务浏览量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="profile-domestic-solution-views">--</div>
                                        <div class="data-compare">较前1周: <span id="profile-domestic-solution-views-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 国内案例专区浏览量 -->
                            <div class="layui-col-md3">
                                <div class="layui-card">
                                    <div class="layui-card-header">国内案例专区浏览量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="profile-domestic-case-views">--</div>
                                        <div class="data-compare">较前1周: <span id="profile-domestic-case-views-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-col-md12" style="margin-top: 15px;">
                        <div class="layui-row layui-col-space15">
                            <!-- 国内技术资料浏览量 -->
                            <div class="layui-col-md4">
                                <div class="layui-card">
                                    <div class="layui-card-header">国内技术资料浏览量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="profile-domestic-tech-views">--</div>
                                        <div class="data-compare">较前1周: <span id="profile-domestic-tech-views-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 海外产品资料浏览量 -->
                            <div class="layui-col-md4">
                                <div class="layui-card">
                                    <div class="layui-card-header">海外产品资料浏览量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="profile-overseas-product-views">--</div>
                                        <div class="data-compare">较前1周: <span id="profile-overseas-product-views-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 展厅设计资料浏览量 -->
                            <div class="layui-col-md4">
                                <div class="layui-card">
                                    <div class="layui-card-header">展厅设计资料浏览量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="profile-showroom-design-views">--</div>
                                        <div class="data-compare">较前1周: <span id="profile-showroom-design-views-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 资料浏览趋势排行 -->
                    <div class="layui-col-md12" style="margin-top: 15px;">
                        <div class="layui-card">
                            <div class="layui-card-header">资料浏览趋势排行</div>
                            <div class="layui-card-body">
                                <table class="layui-table" lay-even lay-skin="row" id="profile-ranking-table"
                                    lay-filter="profile-ranking-table"></table>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>

    <script src="./lib/jquery/dist/jquery.js"></script>
    <script src="./layui/layui.js"></script>
    <script src="./lib/echarts/echarts.min.js"></script>
    <script>
        layui.use(['form', 'element', 'laydate', 'jquery', 'table'], function () {
            var form = layui.form;
            var element = layui.element;
            var laydate = layui.laydate;
            var $ = layui.jquery;

            // ========================================
            // 日期处理工具函数
            // ========================================
            
            // 将Date对象格式化为'YYYY-MM-DD'字符串
            function formatDate(date) {
                if (!date) return '--';
                const year = date.getFullYear();
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                return `${year}-${month}-${day}`;
            }

            // 在Date对象上添加或减去指定天数
            function addDays(date, days) {
                const result = new Date(date);
                result.setDate(result.getDate() + days);
                return result;
            }

            // 在Date对象上添加或减去指定月数（安全处理月末问题）
            function addMonths(date, months) {
                const result = new Date(date);
                const day = result.getDate();
                result.setDate(1); // 设置为当月1号避免溢出
                result.setMonth(result.getMonth() + months);
                // 尝试设置回原始日期，如果超出当月最后一天则限制为最后一天
                const newMonthLastDay = new Date(result.getFullYear(), result.getMonth() + 1, 0).getDate();
                result.setDate(Math.min(day, newMonthLastDay));
                return result;
            }

            // ========================================
            // 全局变量定义
            // ========================================
            
            // 当前选中的时间类型（实时、1天、7天等）
            let currentTimeType = 'realtime';
            
            // 当前选中的时间范围对象
            let currentDateRange = {
                start: null,  // 开始时间
                end: null     // 结束时间
            };

            // ========================================
            // 用户趋势图 ECharts 初始化
            // ========================================
            
            // 初始化用户趋势图实例
            var userChart = echarts.init(document.getElementById('user-chart'));
            
            // 用户趋势图配置选项
            var userChartOption = {
                title: {
                    text: '用户趋势'  // 图表标题
                },
                tooltip: {
                    trigger: 'axis'  // 鼠标悬停显示提示框
                },
                legend: {
                    data: ['新用户', '老用户']  // 图例数据
                },
                xAxis: {
                    type: 'category',  // 分类轴
                    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']  // X轴数据
                },
                yAxis: {
                    type: 'value'  // 数值轴
                },
                series: [
                    {
                        name: '新用户',  // 系列名称
                        type: 'line',    // 图表类型：折线图
                        data: [120, 132, 101, 134, 90, 230, 210]  // 新用户数据
                    },
                    {
                        name: '老用户',  // 系列名称
                        type: 'line',    // 图表类型：折线图
                        data: [220, 182, 191, 234, 290, 330, 310]  // 老用户数据
                    }
                ]
            };
            
            // 应用用户趋势图配置
            userChart.setOption(userChartOption);

            // ========================================
            // 人员类型分析饼图 ECharts 初始化
            // ========================================
            
            // 初始化人员类型分析图表实例
            var userTypeChart = echarts.init(document.getElementById('user-type-chart'));
            
            // 人员类型分析图表配置选项
            var userTypeOption = {
                tooltip: {
                    trigger: 'item'  // 鼠标悬停显示提示框
                },
                legend: {
                    top: '5%',      // 图例位置：顶部5%
                    left: 'center'  // 图例位置：水平居中
                },
                series: [
                    {
                        name: '人员类型',  // 系列名称
                        type: 'pie',      // 图表类型：饼图
                        radius: ['40%', '70%'],  // 饼图半径：内半径40%，外半径70%
                        avoidLabelOverlap: false,  // 避免标签重叠
                        itemStyle: {
                            borderRadius: 10,      // 圆角半径
                            borderColor: '#fff',   // 边框颜色
                            borderWidth: 2         // 边框宽度
                        },
                        label: {
                            show: false,      // 默认不显示标签
                            position: 'center'  // 标签位置：中心
                        },
                        emphasis: {
                            label: {
                                show: true,        // 高亮时显示标签
                                fontSize: '20',    // 字体大小
                                fontWeight: 'bold' // 字体粗细
                            }
                        },
                        labelLine: {
                            show: false  // 不显示引导线
                        },
                        data: [
                            { value: 1048, name: '新用户' },   // 新用户数据
                            { value: 735, name: '老用户' },    // 老用户数据
                            { value: 580, name: '游客' },      // 游客数据
                            { value: 484, name: '经销商' }     // 经销商数据
                        ]
                    }
                ]
            };
            
            // 应用人员类型分析图表配置
            userTypeChart.setOption(userTypeOption);

            // ========================================
            // 访客来源分析饼图 ECharts 初始化
            // ========================================
            
            // 初始化访客来源分析图表实例
            var visitorSourceChart = echarts.init(document.getElementById('visitor-source-chart'));
            
            // 访客来源分析图表配置选项
            var visitorSourceOption = {
                tooltip: {
                    trigger: 'item'  // 鼠标悬停显示提示框
                },
                legend: {
                    top: '5%',      // 图例位置：顶部5%
                    left: 'center'  // 图例位置：水平居中
                },
                series: [
                    {
                        name: '访客来源',  // 系列名称
                        type: 'pie',      // 图表类型：饼图
                        radius: ['40%', '70%'],  // 饼图半径：内半径40%，外半径70%
                        avoidLabelOverlap: false,  // 避免标签重叠
                        itemStyle: {
                            borderRadius: 10,      // 圆角半径
                            borderColor: '#fff',   // 边框颜色
                            borderWidth: 2         // 边框宽度
                        },
                        label: {
                            show: false,      // 默认不显示标签
                            position: 'center'  // 标签位置：中心
                        },
                        emphasis: {
                            label: {
                                show: true,        // 高亮时显示标签
                                fontSize: '20',    // 字体大小
                                fontWeight: 'bold' // 字体粗细
                            }
                        },
                        labelLine: {
                            show: false  // 不显示引导线
                        },
                        data: [
                            { value: 1200, name: '搜索引擎' },  // 搜索引擎数据
                            { value: 800, name: '直接访问' },   // 直接访问数据
                            { value: 600, name: '邮件营销' },   // 邮件营销数据
                            { value: 400, name: '联盟广告' },   // 联盟广告数据
                            { value: 300, name: '视频广告' }    // 视频广告数据
                        ]
                    }
                ]
            };
            
            // 应用访客来源分析图表配置
            visitorSourceChart.setOption(visitorSourceOption);

            // ========================================
            // 地区分布数据准备
            // ========================================
            
            // 地区分布表格数据源
            const areaTableData = [
                { rank: 1, province: '重庆市', count: 44, ratio: '20.0%' },   // 重庆数据
                { rank: 2, province: '广东省', count: 33, ratio: '15.0%' },   // 广东数据
                { rank: 3, province: '河南省', count: 20, ratio: '9.0%' },    // 河南数据
                { rank: 4, province: '四川省', count: 18, ratio: '8.0%' },    // 四川数据
                { rank: 5, province: '山东省', count: 15, ratio: '7.0%' },    // 山东数据
                { rank: 6, province: '江苏省', count: 12, ratio: '5.5%' },    // 江苏数据
                { rank: 7, province: '浙江省', count: 10, ratio: '4.5%' },    // 浙江数据
                { rank: 8, province: '河北省', count: 8, ratio: '3.5%' },     // 河北数据
                { rank: 9, province: '安徽省', count: 6, ratio: '2.5%' },     // 安徽数据
                { rank: 10, province: '福建省', count: 5, ratio: '2.0%' }     // 福建数据
            ];

            // 省份名称映射表（用于地图显示）
            const provinceNameMap = {
                "重庆市": "重庆",  // 重庆市映射为重庆
                "广东省": "广东",  // 广东省映射为广东
                "河南省": "河南",  // 河南省映射为河南
                "四川省": "四川",  // 四川省映射为四川
                "山东省": "山东",  // 山东省映射为山东
                "江苏省": "江苏",  // 江苏省映射为江苏
                "浙江省": "浙江",  // 浙江省映射为浙江
                "河北省": "河北",  // 河北省映射为河北
                "安徽省": "安徽",  // 安徽省映射为安徽
                "福建省": "福建"   // 福建省映射为福建
            };

            // 生成地图数据（将表格数据转换为地图所需格式）
            const areaMapData = areaTableData.map(item => ({
                name: provinceNameMap[item.province] || item.province,  // 省份名称
                value: item.count  // 人数
            }));

            // ========================================
            // 地区分布地图 ECharts 初始化
            // ========================================
            
            // 初始化地区分布地图实例
            var areaMapChart = echarts.init(document.getElementById('area-map-chart'));

            // 加载中国地图GeoJSON数据
            $.get('layuiadmin/map/geojson', function (geoJson) {
                // 注册中国地图
                echarts.registerMap('china', geoJson);

                // 省份数据（用于地图着色）
                var provinceData = [
                    { name: '广东省', value: 100 },  // 广东数据
                    { name: '北京市', value: 80 },   // 北京数据
                    { name: '上海市', value: 60 },   // 上海数据
                    { name: '浙江省', value: 90 },   // 浙江数据
                    { name: '江苏省', value: 70 },   // 江苏数据
                    { name: '重庆市', value: 44 },   // 重庆数据
                    { name: '河南省', value: 20 },   // 河南数据
                    { name: '四川省', value: 18 },   // 四川数据
                    { name: '山东省', value: 15 },   // 山东数据
                    { name: '福建省', value: 5 },    // 福建数据
                    { name: '河北省', value: 8 },    // 河北数据
                    { name: '安徽省', value: 6 }     // 安徽数据
                    // ... 其余省份数据
                ];

                // 计算最大值（用于颜色映射）
                var maxValue = Math.max(...provinceData.map(item => item.value));

                // 地区分布地图配置选项
                var areaMapOption = {
                    tooltip: {
                        trigger: 'item',  // 鼠标悬停显示提示框
                        formatter: function (params) {
                            // 自定义提示框内容
                            if (typeof params.value === 'undefined' || isNaN(params.value)) {
                                return params.name + '<br/>暂无数据';  // 无数据时显示
                            }
                            return params.name + '<br/>人数: ' + params.value;  // 有数据时显示
                        }
                    },
                    visualMap: {
                        min: 0,                    // 最小值
                        max: maxValue,             // 最大值
                        left: 'left',              // 位置：左侧
                        top: 'bottom',             // 位置：底部
                        text: ['高', '低'],         // 文字标签
                        inRange: {
                            color: ['#e0ffff', '#006edd']  // 颜色范围：从浅蓝到深蓝
                        },
                        show: true  // 显示视觉映射组件
                    },
                    series: [
                        {
                            name: '省级分布',        // 系列名称
                            type: 'map',           // 图表类型：地图
                            map: 'china',          // 使用中国地图
                            roam: true,            // 允许缩放和平移
                            zoom: 1.2,             // 初始缩放比例
                            label: {
                                show: true  // 显示省份标签
                            },
                            data: provinceData,    // 省份数据
                            itemStyle: {
                                areaColor: '#f5f5f5',  // 区域颜色：浅灰
                                borderColor: '#999'    // 边框颜色：深灰
                            }
                        }
                    ]
                };

                // 应用地区分布地图配置
                areaMapChart.setOption(areaMapOption);
            });

            // ========================================
            // 地区分布表格 Layui Table 初始化
            // ========================================
            
            // 初始化Layui表格
            layui.use('table', function () {
                var table = layui.table;
                
                // 渲染地区分布表格
                table.render({
                    elem: '#area-distribution-table',  // 表格容器元素
                    cols: [[ // 表头配置
                        { field: 'rank', title: '排名', width: 80, sort: true },      // 排名列
                        { field: 'province', title: '省份' },                         // 省份列
                        { field: 'count', title: '人数', sort: true },                // 人数列
                        { field: 'ratio', title: '占比', sort: true }                 // 占比列
                    ]],
                    data: areaTableData,  // 表格数据
                    page: true,           // 开启分页
                    limit: 10,            // 每页显示10条
                    limits: [10, 20, 30]  // 分页条数选项
                });
            });

            // ========================================
            // 统计时间按钮状态管理
            // ========================================
            
            // 更新按钮选中状态
            function updateButtonState(activeType) {
                // 移除所有按钮的选中状态
                document.querySelectorAll('.layui-btn').forEach(btn => {
                    btn.classList.remove('layui-btn-normal');  // 移除正常状态
                    btn.classList.add('layui-btn-primary');    // 添加主要状态
                });

                // 为当前选中的按钮添加选中状态
                const activeBtn = document.getElementById(activeType + '-btn');
                if (activeBtn) {
                    activeBtn.classList.remove('layui-btn-primary');  // 移除主要状态
                    activeBtn.classList.add('layui-btn-normal');      // 添加正常状态
                }
                
                // 兼容自定义按钮
                if (activeType === 'custom') {
                    document.getElementById('custom-btn').classList.remove('layui-btn-primary');  // 移除主要状态
                    document.getElementById('custom-btn').classList.add('layui-btn-normal');      // 添加正常状态
                }
            }

            // ========================================
            // 时间范围处理函数
            // ========================================
            
            // 根据时间类型计算时间范围
            function handleTimeRange(type) {
                const now = new Date();  // 当前时间
                let start, end;          // 开始和结束时间

                switch (type) {
                    case 'realtime':
                        // 实时：当前时间
                        start = now;
                        end = now;
                        break;
                    case '1day':
                        // 1天：当前时间往前推24小时
                        start = new Date(now.getTime() - 24 * 60 * 60 * 1000);  // 精确到毫秒
                        end = now;
                        break;
                    case '7day':
                        // 7天：当前时间往前推7天
                        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);  // 精确到毫秒
                        end = now;
                        break;
                    case '30day':
                        // 30天：当前时间往前推30天
                        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);  // 精确到毫秒
                        end = now;
                        break;
                    case 'day':
                        // 日：当天开始到当天结束
                        start = new Date(now.getFullYear(), now.getMonth(), now.getDate());  // 当天开始
                        end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);  // 当天结束
                        break;
                    case 'week':
                        // 周：本周开始到本周结束
                        const dayOfWeek = now.getDay();  // 0为周日，6为周六
                        start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek);  // 本周日开始
                        end = new Date(start.getFullYear(), start.getMonth(), start.getDate() + 6, 23, 59, 59, 999);  // 本周六结束
                        break;
                    case 'month':
                        // 月：本月第一天到本月最后一天
                        start = new Date(now.getFullYear(), now.getMonth(), 1);  // 本月第一天开始
                        end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);  // 本月最后一天结束
                        break;
                }

                return { start, end };  // 返回时间范围对象
            }

            // ========================================
            // 仪表板数据更新函数
            // ========================================
            
            // 更新仪表板所有数据
            function updateDashboardData(type, dateRange) {
                // 更新当前时间类型
                currentTimeType = type;
                
                // 更新时间范围
                if (dateRange) {
                    currentDateRange = dateRange;  // 使用传入的时间范围
                } else {
                    currentDateRange = handleTimeRange(type);  // 根据类型计算时间范围
                }

                // 更新按钮状态
                updateButtonState(type);

                // 更新日期显示
                document.getElementById('test-laydate-range-date').value =
                    `${formatDate(currentDateRange.start)} - ${formatDate(currentDateRange.end)}`;

                // 这里添加实际的数据获取逻辑
                // ... 原有的数据更新代码 ...
            }

            // ========================================
            // 统计时间按钮事件监听器
            // ========================================
            
            // 实时按钮点击事件
            document.getElementById('realtime-btn').onclick = function () {
                updateDashboardData('realtime');
            };
            
            // 1天按钮点击事件
            document.getElementById('1day-btn').onclick = function () {
                updateDashboardData('1day');
            };
            
            // 7天按钮点击事件
            document.getElementById('7day-btn').onclick = function () {
                updateDashboardData('7day');
            };
            
            // 30天按钮点击事件
            document.getElementById('30day-btn').onclick = function () {
                updateDashboardData('30day');
            };
            
            // 日按钮点击事件
            document.getElementById('day-btn').onclick = function () {
                updateDashboardData('day');
            };
            
            // 周按钮点击事件
            document.getElementById('week-btn').onclick = function () {
                updateDashboardData('week');
            };
            
            // 月按钮点击事件
            document.getElementById('month-btn').onclick = function () {
                updateDashboardData('month');
            };
            
            // ========================================
            // 自定义日期选择器初始化
            // ========================================
            
            console.log("房价低发撒付===")  // 调试日志
            
            // 初始化隐藏的日期选择器
            laydate.render({
                elem: '#hidden-laydate-input',  // 隐藏的输入框元素
                range: true,                    // 启用日期范围选择
                trigger: 'click',               // 点击触发
                done: function (value, date, endDate) {
                    // 日期选择完成回调
                    if (value) {
                        currentTimeType = 'custom';  // 设置为自定义类型
                        // 设置开始时间
                        currentDateRange.start = new Date(date.year, date.month - 1, date.date);
                        // 设置结束时间（包含当天的最后一毫秒）
                        currentDateRange.end = new Date(endDate.year, endDate.month - 1, endDate.date, 23, 59, 59, 999);
                        // 更新仪表板数据
                        updateDashboardData('custom', currentDateRange);
                    }
                }
            });
            
            // 自定义按钮点击事件
            document.getElementById('custom-btn').onclick = function () {
                // 触发隐藏输入框的点击事件，弹出日期选择器
                document.getElementById('hidden-laydate-input').click();
            };
            
            // ========================================
            // 上一周期按钮事件
            // ========================================
            
            // 上一周期按钮点击事件
            document.getElementById('prev-btn').onclick = function () {
                switch (currentTimeType) {
                    case 'realtime':
                        // 如果是实时，切换到月
                        updateDashboardData('month');
                        break;
                    case '1day':
                        // 如果是1天，切换到实时
                        updateDashboardData('realtime');
                        break;
                    case '7day':
                        // 如果是7天，切换到1天
                        updateDashboardData('1day');
                        break;
                    case '30day':
                        // 如果是30天，切换到7天
                        updateDashboardData('7day');
                        break;
                    case 'day':
                        // 如果是日，切换到30天
                        updateDashboardData('30day');
                        break;
                    case 'week':
                        // 如果是周，切换到日
                        updateDashboardData('day');
                        break;
                    case 'month':
                        // 如果是月，切换到周
                        updateDashboardData('week');
                        break;
                    default:
                        break;
                }
            };

            // ========================================
            // 下一周期按钮事件
            // ========================================
            
            // 下一周期按钮点击事件
            document.getElementById('next-btn').onclick = function () {
                switch (currentTimeType) {
                    case 'realtime':
                        // 如果是实时，切换到1天
                        updateDashboardData('1day');
                        break;
                    case '1day':
                        // 如果是1天，切换到7天
                        updateDashboardData('7day');
                        break;
                    case '7day':
                        // 如果是7天，切换到30天
                        updateDashboardData('30day');
                        break;
                    case '30day':
                        // 如果是30天，切换到日
                        updateDashboardData('day');
                        break;
                    case 'day':
                        // 如果是日，切换到周
                        updateDashboardData('week');
                        break;
                    case 'week':
                        // 如果是周，切换到月
                        updateDashboardData('month');
                        break;
                    case 'month':
                        // 如果是月，切换到实时
                        updateDashboardData('realtime');
                        break;
                    default:
                        break;
                }
            };

            // ========================================
            // 初始化设置
            // ========================================
            
            // 初始化时设置为实时模式
            updateDashboardData('realtime');
        });

        // ========================================
        // Tab切换逻辑
        // ========================================
        
        // 延迟执行，确保DOM元素已加载
        setTimeout(function() {
            // 用户数据分析Tab点击事件
            document.getElementById('user-tab').onclick = function() {
                // 显示用户数据分析区域
                document.getElementById('user-analysis-area').style.display = '';
                // 隐藏资料数据分析区域
                document.getElementById('profile-analysis-area').style.display = 'none';
                // 添加当前Tab的选中状态
                this.classList.add('layui-this');
                // 移除另一个Tab的选中状态
                document.getElementById('profile-tab').classList.remove('layui-this');
            };
            
            // 资料数据分析Tab点击事件
            document.getElementById('profile-tab').onclick = function() {
                console.log("方式附近===")
                // 隐藏用户数据分析区域
                document.getElementById('user-analysis-area').style.display = 'none';
                // 显示资料数据分析区域
                document.getElementById('profile-analysis-area').style.display = '';
                // 添加当前Tab的选中状态
                this.classList.add('layui-this');
                // 移除另一个Tab的选中状态
                document.getElementById('user-tab').classList.remove('layui-this');

                // 重新渲染资料数据分析的图表并初始化
                setTimeout(function() {
                    if (window.profileChart) {
                        window.profileChart.resize();
                    }
                    // 首次切换时初始化资料数据分析的默认状态
                    if (typeof window.profileInitialized === 'undefined') {
                        updateProfileDashboardData('realtime');
                        window.profileInitialized = true;
                    }
                }, 100);
            };
        }, 0);

        // ========================================
        // 【资料数据分析 - 全局变量定义区域】
        // ========================================

        // 当前选中的时间类型（资料数据分析）
        let profileCurrentTimeType = 'realtime';

        // 当前选中的时间范围对象（资料数据分析）
        let profileCurrentDateRange = {
            start: null,  // 开始时间
            end: null     // 结束时间
        };




        // ========================================
        // 【资料数据分析 - 按钮状态管理区域】
        // ========================================

        // 功能：更新资料数据分析按钮选中状态
        // 参数：activeType - 当前激活的时间类型
        // 说明：将指定按钮设为选中状态，其他按钮恢复默认状态
        function updateProfileButtonState(activeType) {
            // 功能：移除所有资料数据分析按钮的选中状态
            document.querySelectorAll('[id^="profile-"][id$="-btn"]').forEach(btn => {
                btn.classList.remove('layui-btn-normal');                // 移除选中状态样式
                btn.classList.add('layui-btn-primary');                  // 添加默认状态样式
            });

            // 功能：为当前选中的按钮添加选中状态
            const activeBtn = document.getElementById('profile-' + activeType + '-btn');
            if (activeBtn) {
                activeBtn.classList.remove('layui-btn-primary');          // 移除默认状态样式
                activeBtn.classList.add('layui-btn-normal');              // 添加选中状态样式
            }
        }




        // ========================================
        // 【资料数据分析 - 仪表板数据更新函数区域】
        // ========================================

        // 功能：更新资料数据分析仪表板的所有数据和显示
        // 参数：type - 时间类型，dateRange - 可选的自定义时间范围
        // 说明：这是资料数据分析的核心更新函数，负责协调所有组件的数据更新
        function updateProfileDashboardData(type, dateRange) {
            // 功能：更新全局当前时间类型变量
            profileCurrentTimeType = type;

            // 功能：更新全局当前时间范围变量
            if (dateRange) {
                profileCurrentDateRange = dateRange;                      // 使用传入的自定义时间范围
            } else {
                profileCurrentDateRange = handleTimeRange(type);          // 根据时间类型计算标准时间范围
            }

            // 功能：更新统计时间按钮的选中状态
            updateProfileButtonState(type);

            // 功能：更新页面上的日期显示输入框
            document.getElementById('profile-laydate-range-date').value =
                `${formatDate(profileCurrentDateRange.start)} - ${formatDate(profileCurrentDateRange.end)}`;

            // 功能：执行实际的数据获取和更新逻辑
            // 说明：这里可以添加Ajax请求获取服务器数据，然后更新图表和统计卡片
            // ... 在这里添加实际的数据获取和更新代码 ...
        }




        // ========================================
        // 【资料数据分析 - 统计时间按钮事件监听器区域】
        // ========================================

        // 功能：资料数据分析实时按钮点击事件处理
        document.getElementById('profile-realtime-btn').onclick = function () {
            updateProfileDashboardData('realtime');
        };

        // 功能：资料数据分析1天按钮点击事件处理
        document.getElementById('profile-1day-btn').onclick = function () {
            updateProfileDashboardData('1day');
        };

        // 功能：资料数据分析7天按钮点击事件处理
        document.getElementById('profile-7day-btn').onclick = function () {
            updateProfileDashboardData('7day');
        };

        // 功能：资料数据分析30天按钮点击事件处理
        document.getElementById('profile-30day-btn').onclick = function () {
            updateProfileDashboardData('30day');
        };

        // 功能：资料数据分析日按钮点击事件处理
        document.getElementById('profile-day-btn').onclick = function () {
            updateProfileDashboardData('day');
        };

        // 功能：资料数据分析周按钮点击事件处理
        document.getElementById('profile-week-btn').onclick = function () {
            updateProfileDashboardData('week');
        };

        // 功能：资料数据分析月按钮点击事件处理
        document.getElementById('profile-month-btn').onclick = function () {
            updateProfileDashboardData('month');
        };




        // ========================================
        // 【资料数据分析 - 自定义日期选择器初始化区域】
        // ========================================

        // 功能：初始化资料数据分析的日期选择器组件
        laydate.render({
            elem: '#profile-hidden-laydate-input',                       // 绑定到隐藏的输入框元素
            range: true,                                                  // 启用日期范围选择模式
            trigger: 'click',                                             // 触发方式：点击触发
            done: function (value, date, endDate) {
                // 功能：日期选择完成后的回调函数
                if (value) {
                    profileCurrentTimeType = 'custom';                    // 设置当前时间类型为自定义
                    // 功能：设置开始时间（当天00:00:00）
                    profileCurrentDateRange.start = new Date(date.year, date.month - 1, date.date);
                    // 功能：设置结束时间（当天23:59:59.999）
                    profileCurrentDateRange.end = new Date(endDate.year, endDate.month - 1, endDate.date, 23, 59, 59, 999);
                    // 功能：使用自定义时间范围更新仪表板数据
                    updateProfileDashboardData('custom', profileCurrentDateRange);
                }
            }
        });

        // 功能：资料数据分析自定义按钮点击事件处理
        document.getElementById('profile-custom-btn').onclick = function () {
            // 功能：程序化触发隐藏输入框的点击事件
            document.getElementById('profile-hidden-laydate-input').click();
        };




        // ========================================
        // 【资料数据分析 - 上一周期按钮事件区域】
        // ========================================

        // 功能：资料数据分析上一周期按钮点击事件处理
        document.getElementById('profile-prev-btn').onclick = function () {
            switch (profileCurrentTimeType) {
                case 'realtime':
                    updateProfileDashboardData('month');                 // 实时 → 月
                    break;
                case '1day':
                    updateProfileDashboardData('realtime');              // 1天 → 实时
                    break;
                case '7day':
                    updateProfileDashboardData('1day');                  // 7天 → 1天
                    break;
                case '30day':
                    updateProfileDashboardData('7day');                  // 30天 → 7天
                    break;
                case 'day':
                    updateProfileDashboardData('30day');                 // 日 → 30天
                    break;
                case 'week':
                    updateProfileDashboardData('day');                   // 周 → 日
                    break;
                case 'month':
                    updateProfileDashboardData('week');                  // 月 → 周
                    break;
                default:
                    break;
            }
        };




        // ========================================
        // 【资料数据分析 - 下一周期按钮事件区域】
        // ========================================

        // 功能：资料数据分析下一周期按钮点击事件处理
        document.getElementById('profile-next-btn').onclick = function () {
            switch (profileCurrentTimeType) {
                case 'realtime':
                    updateProfileDashboardData('1day');                  // 实时 → 1天
                    break;
                case '1day':
                    updateProfileDashboardData('7day');                  // 1天 → 7天
                    break;
                case '7day':
                    updateProfileDashboardData('30day');                 // 7天 → 30天
                    break;
                case '30day':
                    updateProfileDashboardData('day');                   // 30天 → 日
                    break;
                case 'day':
                    updateProfileDashboardData('week');                  // 日 → 周
                    break;
                case 'week':
                    updateProfileDashboardData('month');                 // 周 → 月
                    break;
                case 'month':
                    updateProfileDashboardData('realtime');              // 月 → 实时
                    break;
                default:
                    break;
            }
        };




        // ========================================
        // 【资料数据分析 - ECharts 初始化区域】
        // ========================================
        
        // 功能：初始化资料趋势图实例（设为全局变量以便在Tab切换时重新渲染）
        window.profileChart = echarts.init(document.getElementById('profile-chart'));

        // 功能：配置资料趋势图的所有选项
        // 说明：多条折线图，用于显示资料相关的各种数据趋势
        var profileChartOption = {
            title: { text: '资料趋势' },                                  // 图表主标题
            tooltip: { trigger: 'axis' },                                 // 触发类型：坐标轴触发
            legend: { data: ['资料数', '访问量', '浏览量', '下载量'] },    // 图例显示的数据项名称
            xAxis: {
                type: 'category',
                data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'] // X轴显示的类目数据
            },
            yAxis: { type: 'value' },                                     // Y轴类型：数值轴
            series: [
                {
                    name: '资料数',
                    type: 'line',
                    data: [120, 132, 101, 134, 90, 230, 210]              // 资料数每天的数据
                },
                {
                    name: '访问量',
                    type: 'line',
                    data: [220, 182, 191, 234, 290, 330, 310]             // 访问量每天的数据
                },
                {
                    name: '浏览量',
                    type: 'line',
                    data: [150, 232, 201, 154, 190, 330, 410]             // 浏览量每天的数据
                },
                {
                    name: '下载量',
                    type: 'line',
                    data: [50, 82, 91, 84, 90, 130, 110]                  // 下载量每天的数据
                }
            ]
        };

        // 功能：将配置选项应用到资料趋势图实例
        window.profileChart.setOption(profileChartOption);




        // ========================================
        // 【资料数据分析 - 统计卡片数据填充区域】
        // ========================================

        // 功能：填充资料总览数据
        if(document.getElementById('profile-total-docs')) document.getElementById('profile-total-docs').innerText = '1234';
        if(document.getElementById('profile-total-docs-compare')) document.getElementById('profile-total-docs-compare').innerText = '+5%';

        // 功能：填充访问量数据
        if(document.getElementById('profile-visits')) document.getElementById('profile-visits').innerText = '5678';
        if(document.getElementById('profile-visits-compare')) document.getElementById('profile-visits-compare').innerText = '+3%';

        // 功能：填充浏览量数据
        if(document.getElementById('profile-views')) document.getElementById('profile-views').innerText = '2345';
        if(document.getElementById('profile-views-compare')) document.getElementById('profile-views-compare').innerText = '+2%';

        // 功能：填充下载量数据
        if(document.getElementById('profile-downloads')) document.getElementById('profile-downloads').innerText = '345';
        if(document.getElementById('profile-downloads-compare')) document.getElementById('profile-downloads-compare').innerText = '+1%';

        // 功能：填充收藏量数据
        if(document.getElementById('profile-favorites')) document.getElementById('profile-favorites').innerText = '456';
        if(document.getElementById('profile-favorites-compare')) document.getElementById('profile-favorites-compare').innerText = '-2%';

        // 功能：填充上传量数据
        if(document.getElementById('profile-uploads')) document.getElementById('profile-uploads').innerText = '567';
        if(document.getElementById('profile-uploads-compare')) document.getElementById('profile-uploads-compare').innerText = '+4%';

        // 功能：填充企业宣传资料浏览量数据
        if(document.getElementById('profile-company-views')) document.getElementById('profile-company-views').innerText = '890';
        if(document.getElementById('profile-company-views-compare')) document.getElementById('profile-company-views-compare').innerText = '+6%';

        // 功能：填充国内产品资料浏览量数据
        if(document.getElementById('profile-domestic-product-views')) document.getElementById('profile-domestic-product-views').innerText = '1200';
        if(document.getElementById('profile-domestic-product-views-compare')) document.getElementById('profile-domestic-product-views-compare').innerText = '+8%';

        // 功能：填充国内解决方案服务浏览量数据
        if(document.getElementById('profile-domestic-solution-views')) document.getElementById('profile-domestic-solution-views').innerText = '780';
        if(document.getElementById('profile-domestic-solution-views-compare')) document.getElementById('profile-domestic-solution-views-compare').innerText = '+3%';

        // 功能：填充国内案例专区浏览量数据
        if(document.getElementById('profile-domestic-case-views')) document.getElementById('profile-domestic-case-views').innerText = '650';
        if(document.getElementById('profile-domestic-case-views-compare')) document.getElementById('profile-domestic-case-views-compare').innerText = '+2%';

        // 功能：填充国内技术资料浏览量数据
        if(document.getElementById('profile-domestic-tech-views')) document.getElementById('profile-domestic-tech-views').innerText = '540';
        if(document.getElementById('profile-domestic-tech-views-compare')) document.getElementById('profile-domestic-tech-views-compare').innerText = '+1%';

        // 功能：填充海外产品资料浏览量数据
        if(document.getElementById('profile-overseas-product-views')) document.getElementById('profile-overseas-product-views').innerText = '320';
        if(document.getElementById('profile-overseas-product-views-compare')) document.getElementById('profile-overseas-product-views-compare').innerText = '+5%';

        // 功能：填充展厅设计资料浏览量数据
        if(document.getElementById('profile-showroom-design-views')) document.getElementById('profile-showroom-design-views').innerText = '280';
        if(document.getElementById('profile-showroom-design-views-compare')) document.getElementById('profile-showroom-design-views-compare').innerText = '+7%';




        // ========================================
        // 【资料数据分析 - 资料浏览趋势排行表格数据准备区域】
        // ========================================

        // 功能：定义资料浏览趋势排行表格的数据源
        // 说明：包含排名、分类、类型、标题、次数、占比等信息
        const profileRankingTableData = [
            {
                rank: 1,
                category: '公司品牌宣传',
                type: '企业介绍',
                title: '企业介绍PPT',
                count: 500,
                ratio: '50%'
            },
            {
                rank: 2,
                category: '国内产品资料',
                type: '产品说明书',
                title: 'A30产品说明书',
                count: 300,
                ratio: '25%'
            },
            {
                rank: 3,
                category: '国内解决方案服务',
                type: '解决方案',
                title: '智能制造解决方案',
                count: 200,
                ratio: '15%'
            },
            {
                rank: 4,
                category: '国内案例专区',
                type: '案例分析',
                title: '某大型企业案例',
                count: 150,
                ratio: '10%'
            },
            {
                rank: 5,
                category: '国内技术资料',
                type: '技术文档',
                title: '技术白皮书',
                count: 100,
                ratio: '8%'
            },
            {
                rank: 6,
                category: '海外产品资料',
                type: '产品手册',
                title: '海外产品手册',
                count: 80,
                ratio: '6%'
            },
            {
                rank: 7,
                category: '展厅设计资料',
                type: '设计方案',
                title: '展厅设计方案',
                count: 60,
                ratio: '4%'
            }
        ];




        // ========================================
        // 【资料数据分析 - 资料浏览趋势排行表格 Layui Table 初始化区域】
        // ========================================

        // 功能：初始化资料浏览趋势排行表格
        layui.use('table', function () {
            var table = layui.table;

            // 功能：渲染资料浏览趋势排行表格
            table.render({
                elem: '#profile-ranking-table',                           // 表格容器的DOM元素ID
                cols: [[ // 表头配置数组
                    { field: 'rank', title: '资料编号', width: 100, sort: true },      // 排名列：宽度100px，支持排序
                    { field: 'category', title: '资料分类', width: 150 },              // 分类列：宽度150px
                    { field: 'type', title: '资料类型', width: 120 },                  // 类型列：宽度120px
                    { field: 'title', title: '资料标题', minWidth: 200 },              // 标题列：最小宽度200px
                    { field: 'count', title: '次数', width: 100, sort: true },         // 次数列：宽度100px，支持排序
                    { field: 'ratio', title: '占比', width: 100, sort: true },         // 占比列：宽度100px，支持排序
                    {
                        title: '操作',
                        width: 150,
                        toolbar: '<div><a class="layui-btn layui-btn-xs" lay-event="detail">详情</a> <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="export">导出</a></div>'
                    }  // 操作列：宽度150px，包含详情和导出按钮
                ]],
                data: profileRankingTableData,                            // 表格数据源
                page: true,                                               // 开启分页功能
                limit: 10,                                                // 每页显示10条记录
                limits: [10, 20, 30]                                      // 分页条数选择选项
            });

            // 功能：监听表格工具条事件
            // 说明：处理详情和导出按钮的点击事件
            table.on('tool(profile-ranking-table)', function(obj){
                var data = obj.data;                                      // 获取当前行数据
                if(obj.event === 'detail'){
                    layer.msg('查看详情：' + data.title);                 // 详情按钮点击处理
                } else if(obj.event === 'export'){
                    layer.msg('导出资料：' + data.title);                 // 导出按钮点击处理
                }
            });
        });

        // ========================================
        // 资料数据分析区域统计卡片数据填充
        // ========================================
        
        // 填充资料总览数据
        if(document.getElementById('profile-total-docs')) document.getElementById('profile-total-docs').innerText = '1234';
        if(document.getElementById('profile-total-docs-compare')) document.getElementById('profile-total-docs-compare').innerText = '+5%';
        
        // 填充访问量数据
        if(document.getElementById('profile-visits')) document.getElementById('profile-visits').innerText = '5678';
        if(document.getElementById('profile-visits-compare')) document.getElementById('profile-visits-compare').innerText = '+3%';
        
        // 填充浏览量数据
        if(document.getElementById('profile-views')) document.getElementById('profile-views').innerText = '2345';
        if(document.getElementById('profile-views-compare')) document.getElementById('profile-views-compare').innerText = '+2%';
        
        // 填充下载量数据
        if(document.getElementById('profile-downloads')) document.getElementById('profile-downloads').innerText = '345';
        if(document.getElementById('profile-downloads-compare')) document.getElementById('profile-downloads-compare').innerText = '+1%';
        
        // 填充收藏量数据
        if(document.getElementById('profile-favorites')) document.getElementById('profile-favorites').innerText = '456';
        if(document.getElementById('profile-favorites-compare')) document.getElementById('profile-favorites-compare').innerText = '-2%';
        
        // 填充上传量数据
        if(document.getElementById('profile-uploads')) document.getElementById('profile-uploads').innerText = '78';
        if(document.getElementById('profile-uploads-compare')) document.getElementById('profile-uploads-compare').innerText = '+0%';


    </script>
</body>

</html>