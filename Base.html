<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title></title>
    <link rel="stylesheet" href="./layui/css/layui.css">
    <style>
        body {
            margin: 0;
            padding: 15px;
            background-color: #f2f2f2;
        }

        .layui-card-header {
            font-size: 16px;
            font-weight: bold;
        }

        .data-overview {
            text-align: center;
            padding: 20px 0;
        }

        .data-value {
            font-size: 28px;
            color: #333;
            margin-bottom: 5px;
        }

        .data-compare {
            font-size: 14px;
            color: #999;
        }

        .layui-col-md2,
        .layui-col-md10 {
            padding: 5px;

        }

        .layui-tab-brief>.layui-tab-title .layui-this {
            color: #009688;
        }

        .layui-tab-brief>.layui-tab-title .layui-this:after {
            border-bottom: 2px solid #009688;
        }
    </style>
</head>

<body>
    <div class="layui-fluid">
        <div class="layui-row">
            <div class="layui-col-md12">
                <div class="layui-tab layui-tab-brief">
                    <ul class="layui-tab-title">
                        <li class="layui-this" id="user-tab">用户数据分析</li>
                        <li id="profile-tab">资料数据分析</li>
                    </ul>
                </div>
            </div>

            <div class="layui-col-md12">
                <div id="user-analysis-area">
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="layui-form layui-form-pane">
                                <div  style="align-items: center;display: flex;width: 100%;">
                                    <label class="layui-form-label" style="width: 140px;">统计时间</label>
                                    <div class="layui-input-inline">
                                        <input type="text" class="layui-input" style="width:200px;" id="test-laydate-range-date"
                                            placeholder="请选择日期" disabled>
                                    </div>
                                    <div class="layui-input-inline" style="display: flex;justify-content: flex-end;width: 100%;">
                                        <button class="layui-btn layui-btn-primary layui-btn-sm"
                                            id="realtime-btn">实时</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="1day-btn">1天</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="7day-btn">7天</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="30day-btn">30天</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="day-btn">日</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="week-btn">周</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="month-btn">月</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" style="position: relative;"
                                            id="custom-btn">自定义
                                            <div style="width:100%;height:100%;position:absolute;background: red;top: 0;left: 0;opacity:0;"
                                                id="hidden-laydate-input">12312</div>
                                        </button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="prev-btn"><i
                                                class="layui-icon layui-icon-left"></i></button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="next-btn"><i
                                                class="layui-icon layui-icon-right"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-col-md12">
                        <div class="layui-row layui-col-space15">

                            <!-- 折线图区域 -->
                            <div>
                                <div class="layui-card">
                                    <div class="layui-card-header">用户趋势图</div>
                                    <div class="layui-card-body">
                                        <div id="user-chart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-col-md12" style="margin-top: 15px;">
                        <div class="layui-row layui-col-space15">
                            <!-- 用户总览 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">用户总览</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="total-users">--</div>
                                        <div class="data-compare">较前1周: <span id="total-users-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 老用户数 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">老用户数</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="old-users">--</div>
                                        <div class="data-compare">较前1周: <span id="old-users-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 新用户数 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">新用户数</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="new-users">--</div>
                                        <div class="data-compare">较前1周: <span id="new-users-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 游客用户数量 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">游客用户数量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="visitor-users">--</div>
                                        <div class="data-compare">较前1周: <span id="visitor-users-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 游客注册数量 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">游客注册数量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="registered-visitors">--</div>
                                        <div class="data-compare">较前1周: <span id="registered-visitors-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 经销商 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">经销商</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="dealers">--</div>
                                        <div class="data-compare">较前1周: <span id="dealers-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 平台用户属性分析 -->
                    <!-- <div class="layui-col-md12">
                        <div class="layui-tab layui-tab-brief" style="margin-top: 15px;">
                            <ul class="layui-tab-title">
                                <li class="layui-this">平台用户属性分析</li>
                            </ul>
                        </div>
                    </div> -->

                    <div class="layui-col-md12" style="margin-top: 15px;">
                        <div class="layui-row ">
                            <!-- 人员类型分析 -->
                            <div class="layui-col-md6">
                                <div class="layui-card">
                                    <div class="layui-card-header">人员类型分析</div>
                                    <div class="layui-card-body">
                                        <div id="user-type-chart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- 访客来源分析 -->
                            <div class="layui-col-md6">
                                <div class="layui-card">
                                    <div class="layui-card-header">访客来源分析</div>
                                    <div class="layui-card-body">
                                        <div id="visitor-source-chart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 地区分布 -->
                    <div class="layui-col-md12" style="margin-top: 15px;">
                        <div class="layui-card">
                            <div class="layui-card-header">地区分布</div>
                            <div class="layui-card-body layui-row">
                                <div class="layui-col-md6">
                                    <div id="area-map-chart" style="height: 400px;"></div>
                                </div>
                                <div class="layui-col-md6">
                                    <table class="layui-table" lay-even lay-skin="row" id="area-distribution-table"
                                        lay-filter="area-distribution-table"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="profile-analysis-area">

                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="layui-form layui-form-pane">
                                <div  style="align-items: center;display: flex;width: 100%;">
                                    <label class="layui-form-label" style="width: 140px;">统计时间</label>
                                    <div class="layui-input-inline">
                                        <input type="text" class="layui-input" style="width:200px;" id="test-laydate-range-date"
                                            placeholder="请选择日期" disabled>
                                    </div>
                                    <div class="layui-input-inline" style="display: flex;justify-content: flex-end;width: 100%;">
                                        <button class="layui-btn layui-btn-primary layui-btn-sm"
                                            id="realtime-btn">实时</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="1day-btn">1天</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="7day-btn">7天</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="30day-btn">30天</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="day-btn">日</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="week-btn">周</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="month-btn">月</button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" style="position: relative;"
                                            id="custom-btn">自定义
                                            <div style="width:100%;height:100%;position:absolute;background: red;top: 0;left: 0;opacity:0;"
                                                id="hidden-laydate-input">12312</div>
                                        </button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="prev-btn"><i
                                                class="layui-icon layui-icon-left"></i></button>
                                        <button class="layui-btn layui-btn-primary layui-btn-sm" id="next-btn"><i
                                                class="layui-icon layui-icon-right"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-col-md12">
                        <div class="layui-row layui-col-space15">

                            <!-- 折线图区域 -->
                            <div>
                                <div class="layui-card">
                                    <div class="layui-card-header">用户趋势图</div>
                                    <div class="layui-card-body">
                                        <div id="user-chart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="layui-col-md12" style="margin-top: 15px;">
                        <div class="layui-row layui-col-space15">
                            <!-- 用户总览 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">用户总览</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="total-users">--</div>
                                        <div class="data-compare">较前1周: <span id="total-users-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 老用户数 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">老用户数</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="old-users">--</div>
                                        <div class="data-compare">较前1周: <span id="old-users-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 新用户数 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">新用户数</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="new-users">--</div>
                                        <div class="data-compare">较前1周: <span id="new-users-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 游客用户数量 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">游客用户数量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="visitor-users">--</div>
                                        <div class="data-compare">较前1周: <span id="visitor-users-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 游客注册数量 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">游客注册数量</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="registered-visitors">--</div>
                                        <div class="data-compare">较前1周: <span id="registered-visitors-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 经销商 -->
                            <div class="layui-col-md2">
                                <div class="layui-card">
                                    <div class="layui-card-header">经销商</div>
                                    <div class="layui-card-body data-overview">
                                        <div class="data-value" id="dealers">--</div>
                                        <div class="data-compare">较前1周: <span id="dealers-compare">--</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md12" style="margin-top: 15px;">
                        <div class="layui-row ">
                            <!-- 人员类型分析 -->
                            <div class="layui-col-md6">
                                <div class="layui-card">
                                    <div class="layui-card-header">人员类型分析</div>
                                    <div class="layui-card-body">
                                        <div id="user-type-chart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- 访客来源分析 -->
                            <div class="layui-col-md6">
                                <div class="layui-card">
                                    <div class="layui-card-header">访客来源分析</div>
                                    <div class="layui-card-body">
                                        <div id="visitor-source-chart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 地区分布 -->
                    <div class="layui-col-md12" style="margin-top: 15px;">
                        <div class="layui-card">
                            <div class="layui-card-header">地区分布</div>
                            <div class="layui-card-body layui-row">
                                <div class="layui-col-md6">
                                    <div id="area-map-chart" style="height: 400px;"></div>
                                </div>
                                <div class="layui-col-md6">
                                    <table class="layui-table" lay-even lay-skin="row" id="area-distribution-table"
                                        lay-filter="area-distribution-table"></table>
                                </div>
                            </div>
                        </div>
                    </div>


                    
                    
                </div>
            </div>
        </div>
    </div>

    <script src="./lib/jquery/dist/jquery.js"></script>
    <script src="./layui/layui.js"></script>
    <script src="./lib/echarts/echarts.min.js"></script>
    <script>
        layui.use(['form', 'element', 'laydate', 'jquery', 'table'], function () {
            // ========================================
            // 【Layui模块初始化】
            // ========================================
            var form = layui.form;        // 表单模块
            var element = layui.element;  // 元素模块
            var laydate = layui.laydate;  // 日期模块
            var $ = layui.jquery;         // jQuery模块




            // ========================================
            // 【日期处理工具函数区域】
            // ========================================

            // 功能：将Date对象格式化为'YYYY-MM-DD'字符串
            // 参数：date - Date对象
            // 返回：格式化后的日期字符串，如果date为空则返回'--'
            function formatDate(date) {
                if (!date) return '--';                                          // 如果日期为空，返回占位符
                const year = date.getFullYear();                                 // 获取年份
                const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 获取月份并补零
                const day = date.getDate().toString().padStart(2, '0');          // 获取日期并补零
                return `${year}-${month}-${day}`;                                // 返回格式化字符串
            }

            // 功能：在Date对象上添加或减去指定天数
            // 参数：date - 原始Date对象，days - 要添加的天数（负数表示减去）
            // 返回：新的Date对象
            function addDays(date, days) {
                const result = new Date(date);                // 创建新的Date对象副本
                result.setDate(result.getDate() + days);      // 设置新的日期
                return result;                                // 返回新的Date对象
            }

            // 功能：在Date对象上添加或减去指定月数（安全处理月末问题）
            // 参数：date - 原始Date对象，months - 要添加的月数（负数表示减去）
            // 返回：新的Date对象
            function addMonths(date, months) {
                const result = new Date(date);                                                          // 创建新的Date对象副本
                const day = result.getDate();                                                           // 保存原始日期
                result.setDate(1);                                                                      // 设置为当月1号避免溢出
                result.setMonth(result.getMonth() + months);                                            // 设置新的月份
                const newMonthLastDay = new Date(result.getFullYear(), result.getMonth() + 1, 0).getDate(); // 获取新月份的最后一天
                result.setDate(Math.min(day, newMonthLastDay));                                         // 设置日期，避免超出月末
                return result;                                                                          // 返回新的Date对象
            }




            // ========================================
            // 【用户数据分析 - 全局变量定义区域】
            // ========================================

            // 当前选中的时间类型（实时、1天、7天、30天、日、周、月、自定义）
            let currentTimeType = 'realtime';

            // 当前选中的时间范围对象
            let currentDateRange = {
                start: null,  // 开始时间
                end: null     // 结束时间
            };

            // ========================================
            // 【用户数据分析 - 用户趋势图 ECharts 初始化区域】
            // ========================================

            // 功能：初始化用户趋势图表实例
            // 说明：获取页面中id为'user-chart'的DOM元素，并创建ECharts实例
            var userChart = echarts.init(document.getElementById('user-chart'));

            // 功能：配置用户趋势图的所有选项
            // 说明：包含图表标题、提示框、图例、坐标轴、数据系列等配置
            var userChartOption = {
                title: {
                    text: '用户趋势'                                              // 图表主标题
                },
                tooltip: {
                    trigger: 'axis'                                               // 触发类型：坐标轴触发（鼠标悬停在X轴上显示该点所有数据）
                },
                legend: {
                    data: ['新用户', '老用户']                                    // 图例显示的数据项名称
                },
                xAxis: {
                    type: 'category',                                             // X轴类型：类目轴（离散数据）
                    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']  // X轴显示的类目数据
                },
                yAxis: {
                    type: 'value'                                                 // Y轴类型：数值轴（连续数据）
                },
                series: [
                    {
                        name: '新用户',                                           // 数据系列名称（对应图例）
                        type: 'line',                                             // 图表类型：折线图
                        data: [120, 132, 101, 134, 90, 230, 210]                 // 新用户每天的数据
                    },
                    {
                        name: '老用户',                                           // 数据系列名称（对应图例）
                        type: 'line',                                             // 图表类型：折线图
                        data: [220, 182, 191, 234, 290, 330, 310]                // 老用户每天的数据
                    }
                ]
            };

            // 功能：将配置选项应用到图表实例
            // 说明：使图表根据配置显示出来
            userChart.setOption(userChartOption);

            // ========================================
            // 【用户数据分析 - 人员类型分析饼图 ECharts 初始化区域】
            // ========================================

            // 功能：初始化人员类型分析饼图实例
            // 说明：获取页面中id为'user-type-chart'的DOM元素，并创建ECharts实例
            var userTypeChart = echarts.init(document.getElementById('user-type-chart'));

            // 功能：配置人员类型分析饼图的所有选项
            // 说明：环形饼图，用于显示不同人员类型的占比分布
            var userTypeOption = {
                tooltip: {
                    trigger: 'item'                                               // 触发类型：数据项触发（鼠标悬停在饼图块上显示该项数据）
                },
                legend: {
                    top: '5%',                                                    // 图例距离顶部的位置
                    left: 'center'                                                // 图例水平居中
                },
                series: [
                    {
                        name: '人员类型',                                         // 系列名称（显示在提示框中）
                        type: 'pie',                                              // 图表类型：饼图
                        radius: ['40%', '70%'],                                   // 饼图半径：[内半径, 外半径] 形成环形
                        avoidLabelOverlap: false,                                 // 是否避免标签重叠
                        itemStyle: {
                            borderRadius: 10,                                     // 饼图块的圆角半径
                            borderColor: '#fff',                                  // 饼图块的边框颜色
                            borderWidth: 2                                        // 饼图块的边框宽度
                        },
                        label: {
                            show: false,                                          // 默认不显示标签
                            position: 'center'                                    // 标签位置：中心
                        },
                        emphasis: {                                               // 高亮状态配置（鼠标悬停时）
                            label: {
                                show: true,                                       // 高亮时显示标签
                                fontSize: '20',                                   // 高亮时标签字体大小
                                fontWeight: 'bold'                                // 高亮时标签字体粗细
                            }
                        },
                        labelLine: {
                            show: false                                           // 不显示标签引导线
                        },
                        data: [
                            { value: 1048, name: '新用户' },                     // 新用户数据：数值和名称
                            { value: 735, name: '老用户' },                      // 老用户数据：数值和名称
                            { value: 580, name: '游客' },                        // 游客数据：数值和名称
                            { value: 484, name: '经销商' }                       // 经销商数据：数值和名称
                        ]
                    }
                ]
            };

            // 功能：将配置选项应用到人员类型分析图表实例
            // 说明：使图表根据配置显示出来
            userTypeChart.setOption(userTypeOption);

            // ========================================
            // 【用户数据分析 - 访客来源分析饼图 ECharts 初始化区域】
            // ========================================

            // 功能：初始化访客来源分析饼图实例
            // 说明：获取页面中id为'visitor-source-chart'的DOM元素，并创建ECharts实例
            var visitorSourceChart = echarts.init(document.getElementById('visitor-source-chart'));

            // 功能：配置访客来源分析饼图的所有选项
            // 说明：环形饼图，用于显示不同访客来源渠道的占比分布
            var visitorSourceOption = {
                tooltip: {
                    trigger: 'item'                                               // 触发类型：数据项触发（鼠标悬停在饼图块上显示该项数据）
                },
                legend: {
                    top: '5%',                                                    // 图例距离顶部的位置
                    left: 'center'                                                // 图例水平居中
                },
                series: [
                    {
                        name: '访客来源',                                         // 系列名称（显示在提示框中）
                        type: 'pie',                                              // 图表类型：饼图
                        radius: ['40%', '70%'],                                   // 饼图半径：[内半径, 外半径] 形成环形
                        avoidLabelOverlap: false,                                 // 是否避免标签重叠
                        itemStyle: {
                            borderRadius: 10,                                     // 饼图块的圆角半径
                            borderColor: '#fff',                                  // 饼图块的边框颜色
                            borderWidth: 2                                        // 饼图块的边框宽度
                        },
                        label: {
                            show: false,                                          // 默认不显示标签
                            position: 'center'                                    // 标签位置：中心
                        },
                        emphasis: {                                               // 高亮状态配置（鼠标悬停时）
                            label: {
                                show: true,                                       // 高亮时显示标签
                                fontSize: '20',                                   // 高亮时标签字体大小
                                fontWeight: 'bold'                                // 高亮时标签字体粗细
                            }
                        },
                        labelLine: {
                            show: false                                           // 不显示标签引导线
                        },
                        data: [
                            { value: 1200, name: '搜索引擎' },                    // 搜索引擎来源数据：数值和名称
                            { value: 800, name: '直接访问' },                     // 直接访问来源数据：数值和名称
                            { value: 600, name: '邮件营销' },                     // 邮件营销来源数据：数值和名称
                            { value: 400, name: '联盟广告' },                     // 联盟广告来源数据：数值和名称
                            { value: 300, name: '视频广告' }                      // 视频广告来源数据：数值和名称
                        ]
                    }
                ]
            };

            // 功能：将配置选项应用到访客来源分析图表实例
            // 说明：使图表根据配置显示出来
            visitorSourceChart.setOption(visitorSourceOption);

            // ========================================
            // 【用户数据分析 - 地区分布数据准备区域】
            // ========================================

            // 功能：定义地区分布表格的数据源
            // 说明：包含排名、省份、人数、占比等信息，用于表格显示和地图数据转换
            const areaTableData = [
                { rank: 1, province: '重庆市', count: 44, ratio: '20.0%' },   // 排名第1：重庆市数据
                { rank: 2, province: '广东省', count: 33, ratio: '15.0%' },   // 排名第2：广东省数据
                { rank: 3, province: '河南省', count: 20, ratio: '9.0%' },    // 排名第3：河南省数据
                { rank: 4, province: '四川省', count: 18, ratio: '8.0%' },    // 排名第4：四川省数据
                { rank: 5, province: '山东省', count: 15, ratio: '7.0%' },    // 排名第5：山东省数据
                { rank: 6, province: '江苏省', count: 12, ratio: '5.5%' },    // 排名第6：江苏省数据
                { rank: 7, province: '浙江省', count: 10, ratio: '4.5%' },    // 排名第7：浙江省数据
                { rank: 8, province: '河北省', count: 8, ratio: '3.5%' },     // 排名第8：河北省数据
                { rank: 9, province: '安徽省', count: 6, ratio: '2.5%' },     // 排名第9：安徽省数据
                { rank: 10, province: '福建省', count: 5, ratio: '2.0%' }     // 排名第10：福建省数据
            ];

            // 功能：省份名称映射表
            // 说明：将完整省份名称映射为地图中使用的简称，确保地图能正确识别省份
            const provinceNameMap = {
                "重庆市": "重庆",                                              // 重庆市 → 重庆
                "广东省": "广东",                                              // 广东省 → 广东
                "河南省": "河南",                                              // 河南省 → 河南
                "四川省": "四川",                                              // 四川省 → 四川
                "山东省": "山东",                                              // 山东省 → 山东
                "江苏省": "江苏",                                              // 江苏省 → 江苏
                "浙江省": "浙江",                                              // 浙江省 → 浙江
                "河北省": "河北",                                              // 河北省 → 河北
                "安徽省": "安徽",                                              // 安徽省 → 安徽
                "福建省": "福建"                                               // 福建省 → 福建
            };

            // 功能：生成地图所需的数据格式
            // 说明：将表格数据转换为ECharts地图组件所需的数据格式
            const areaMapData = areaTableData.map(item => ({
                name: provinceNameMap[item.province] || item.province,         // 使用映射后的省份名称，如果没有映射则使用原名称
                value: item.count                                              // 该省份的人数数值
            }));

            // ========================================
            // 【用户数据分析 - 地区分布地图 ECharts 初始化区域】
            // ========================================

            // 功能：初始化地区分布地图实例
            // 说明：获取页面中id为'area-map-chart'的DOM元素，并创建ECharts实例
            var areaMapChart = echarts.init(document.getElementById('area-map-chart'));

            // 功能：异步加载中国地图GeoJSON数据并初始化地图
            // 说明：通过Ajax请求获取地图数据，然后注册地图并配置显示选项
            $.get('layuiadmin/map/geojson', function (geoJson) {
                // 功能：注册中国地图到ECharts
                // 说明：将获取到的GeoJSON数据注册为名为'china'的地图，供后续使用
                echarts.registerMap('china', geoJson);

                // 功能：定义省份数据用于地图着色
                // 说明：包含各省份的名称和对应的数值，数值越大颜色越深
                var provinceData = [
                    { name: '广东省', value: 100 },                           // 广东省：100人
                    { name: '北京市', value: 80 },                            // 北京市：80人
                    { name: '上海市', value: 60 },                            // 上海市：60人
                    { name: '浙江省', value: 90 },                            // 浙江省：90人
                    { name: '江苏省', value: 70 },                            // 江苏省：70人
                    { name: '重庆市', value: 44 },                            // 重庆市：44人
                    { name: '河南省', value: 20 },                            // 河南省：20人
                    { name: '四川省', value: 18 },                            // 四川省：18人
                    { name: '山东省', value: 15 },                            // 山东省：15人
                    { name: '福建省', value: 5 },                             // 福建省：5人
                    { name: '河北省', value: 8 },                             // 河北省：8人
                    { name: '安徽省', value: 6 }                              // 安徽省：6人
                    // 注：可以继续添加其他省份的数据
                ];

                // 功能：计算数据中的最大值
                // 说明：用于设置视觉映射组件的最大值，确保颜色映射的准确性
                var maxValue = Math.max(...provinceData.map(item => item.value));

                // 功能：配置地区分布地图的所有选项
                // 说明：包含提示框、视觉映射、数据系列等配置
                var areaMapOption = {
                    tooltip: {
                        trigger: 'item',                                      // 触发类型：数据项触发（鼠标悬停在省份上显示该省数据）
                        formatter: function (params) {
                            // 功能：自定义提示框显示内容
                            // 说明：根据是否有数据显示不同的提示信息
                            if (typeof params.value === 'undefined' || isNaN(params.value)) {
                                return params.name + '<br/>暂无数据';         // 无数据时的显示格式
                            }
                            return params.name + '<br/>人数: ' + params.value; // 有数据时的显示格式
                        }
                    },
                    visualMap: {                                              // 视觉映射组件：用于颜色映射
                        min: 0,                                               // 数据最小值
                        max: maxValue,                                        // 数据最大值（动态计算）
                        left: 'left',                                         // 组件位置：左侧
                        top: 'bottom',                                        // 组件位置：底部
                        text: ['高', '低'],                                   // 组件两端的文字标签
                        inRange: {
                            color: ['#e0ffff', '#006edd']                     // 颜色映射范围：浅蓝色到深蓝色
                        },
                        show: true                                            // 显示视觉映射组件
                    },
                    series: [
                        {
                            name: '省级分布',                                 // 数据系列名称
                            type: 'map',                                      // 图表类型：地图
                            map: 'china',                                     // 使用的地图：中国地图
                            roam: true,                                       // 允许用户缩放和平移地图
                            zoom: 1.2,                                        // 初始缩放比例
                            label: {
                                show: true                                    // 显示省份名称标签
                            },
                            data: provinceData,                               // 省份数据
                            itemStyle: {
                                areaColor: '#f5f5f5',                        // 默认区域颜色：浅灰色
                                borderColor: '#999'                           // 省份边框颜色：深灰色
                            }
                        }
                    ]
                };

                // 功能：将配置选项应用到地图实例
                // 说明：使地图根据配置显示出来
                areaMapChart.setOption(areaMapOption);
            });

            // ========================================
            // 【用户数据分析 - 地区分布表格 Layui Table 初始化区域】
            // ========================================

            // 功能：初始化Layui表格模块并渲染地区分布表格
            // 说明：使用Layui的table模块创建一个可排序、可分页的数据表格
            layui.use('table', function () {
                var table = layui.table;                                     // 获取table模块

                // 功能：渲染地区分布表格
                // 说明：配置表格的列、数据源、分页等选项
                table.render({
                    elem: '#area-distribution-table',                        // 表格容器的DOM元素ID
                    cols: [[ // 表头配置数组
                        { field: 'rank', title: '排名', width: 80, sort: true },      // 排名列：宽度80px，支持排序
                        { field: 'province', title: '省份' },                         // 省份列：自适应宽度
                        { field: 'count', title: '人数', sort: true },                // 人数列：支持排序
                        { field: 'ratio', title: '占比', sort: true }                 // 占比列：支持排序
                    ]],
                    data: areaTableData,                                      // 表格数据源（使用前面定义的数据）
                    page: true,                                               // 开启分页功能
                    limit: 10,                                                // 每页显示10条记录
                    limits: [10, 20, 30]                                      // 分页条数选择选项
                });
            });

            // ========================================
            // 【用户数据分析 - 统计时间按钮状态管理区域】
            // ========================================

            // 功能：更新统计时间按钮的选中状态
            // 参数：activeType - 当前激活的时间类型（如'realtime', '1day', '7day'等）
            // 说明：将指定按钮设为选中状态，其他按钮恢复默认状态
            function updateButtonState(activeType) {
                // 功能：移除所有按钮的选中状态
                // 说明：遍历页面中所有layui按钮，将它们重置为默认的primary状态
                document.querySelectorAll('.layui-btn').forEach(btn => {
                    btn.classList.remove('layui-btn-normal');                // 移除选中状态的样式类
                    btn.classList.add('layui-btn-primary');                  // 添加默认状态的样式类
                });

                // 功能：为当前选中的按钮添加选中状态
                // 说明：根据传入的activeType找到对应按钮并设置为选中状态
                const activeBtn = document.getElementById(activeType + '-btn');
                if (activeBtn) {
                    activeBtn.classList.remove('layui-btn-primary');          // 移除默认状态样式
                    activeBtn.classList.add('layui-btn-normal');              // 添加选中状态样式
                }

                // 功能：特殊处理自定义按钮
                // 说明：自定义按钮的ID不遵循常规命名规则，需要单独处理
                if (activeType === 'custom') {
                    document.getElementById('custom-btn').classList.remove('layui-btn-primary');  // 移除默认状态
                    document.getElementById('custom-btn').classList.add('layui-btn-normal');      // 添加选中状态
                }
            }

            // ========================================
            // 【用户数据分析 - 时间范围处理函数区域】
            // ========================================

            // 功能：根据时间类型计算对应的时间范围
            // 参数：type - 时间类型字符串（'realtime', '1day', '7day', '30day', 'day', 'week', 'month'）
            // 返回：包含start和end属性的时间范围对象
            // 说明：将用户选择的时间类型转换为具体的开始和结束时间
            function handleTimeRange(type) {
                const now = new Date();                                       // 获取当前时间作为基准
                let start, end;                                               // 声明开始时间和结束时间变量

                switch (type) {
                    case 'realtime':
                        // 实时模式：开始时间和结束时间都是当前时间
                        start = now;                                          // 开始时间 = 当前时间
                        end = now;                                            // 结束时间 = 当前时间
                        break;
                    case '1day':
                        // 1天模式：从24小时前到现在
                        start = new Date(now.getTime() - 24 * 60 * 60 * 1000);  // 开始时间 = 当前时间 - 24小时（毫秒计算）
                        end = now;                                            // 结束时间 = 当前时间
                        break;
                    case '7day':
                        // 7天模式：从7天前到现在
                        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);  // 开始时间 = 当前时间 - 7天（毫秒计算）
                        end = now;                                            // 结束时间 = 当前时间
                        break;
                    case '30day':
                        // 30天模式：从30天前到现在
                        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 开始时间 = 当前时间 - 30天（毫秒计算）
                        end = now;                                            // 结束时间 = 当前时间
                        break;
                    case 'day':
                        // 日模式：当天的00:00:00到23:59:59
                        start = new Date(now.getFullYear(), now.getMonth(), now.getDate()); // 开始时间 = 当天00:00:00
                        end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999); // 结束时间 = 当天23:59:59.999
                        break;
                    case 'week':
                        // 周模式：本周日00:00:00到本周六23:59:59
                        const dayOfWeek = now.getDay();                      // 获取当前是周几（0=周日，1=周一...6=周六）
                        start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek); // 开始时间 = 本周日00:00:00
                        end = new Date(start.getFullYear(), start.getMonth(), start.getDate() + 6, 23, 59, 59, 999); // 结束时间 = 本周六23:59:59.999
                        break;
                    case 'month':
                        // 月模式：本月第一天00:00:00到本月最后一天23:59:59
                        start = new Date(now.getFullYear(), now.getMonth(), 1); // 开始时间 = 本月第一天00:00:00
                        end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999); // 结束时间 = 本月最后一天23:59:59.999
                        break;
                }

                return { start, end };                                        // 返回包含开始和结束时间的对象
            }

            // ========================================
            // 【用户数据分析 - 仪表板数据更新函数区域】
            // ========================================

            // 功能：更新用户数据分析仪表板的所有数据和显示
            // 参数：type - 时间类型，dateRange - 可选的自定义时间范围
            // 说明：这是用户数据分析的核心更新函数，负责协调所有组件的数据更新
            function updateDashboardData(type, dateRange) {
                // 功能：更新全局当前时间类型变量
                // 说明：保存用户选择的时间类型，供其他函数使用
                currentTimeType = type;

                // 功能：更新全局当前时间范围变量
                // 说明：根据是否传入自定义时间范围来决定使用方式
                if (dateRange) {
                    currentDateRange = dateRange;                             // 使用传入的自定义时间范围
                } else {
                    currentDateRange = handleTimeRange(type);                 // 根据时间类型计算标准时间范围
                }

                // 功能：更新统计时间按钮的选中状态
                // 说明：调用按钮状态管理函数，高亮当前选中的时间类型按钮
                updateButtonState(type);

                // 功能：更新页面上的日期显示输入框
                // 说明：将计算出的时间范围格式化后显示在日期选择器中
                document.getElementById('test-laydate-range-date').value =
                    `${formatDate(currentDateRange.start)} - ${formatDate(currentDateRange.end)}`;

                // 功能：执行实际的数据获取和更新逻辑
                // 说明：这里可以添加Ajax请求获取服务器数据，然后更新图表和统计卡片
                // 示例：可以调用API获取用户数据，然后更新ECharts图表和数值显示
                // ... 在这里添加实际的数据获取和更新代码 ...
            }

            // ========================================
            // 【用户数据分析 - 统计时间按钮事件监听器区域】
            // ========================================

            // 功能：实时按钮点击事件处理
            // 说明：用户点击"实时"按钮时，切换到实时数据模式
            document.getElementById('realtime-btn').onclick = function () {
                updateDashboardData('realtime');                             // 调用数据更新函数，传入'realtime'类型
            };

            // 功能：1天按钮点击事件处理
            // 说明：用户点击"1天"按钮时，显示最近24小时的数据
            document.getElementById('1day-btn').onclick = function () {
                updateDashboardData('1day');                                 // 调用数据更新函数，传入'1day'类型
            };

            // 功能：7天按钮点击事件处理
            // 说明：用户点击"7天"按钮时，显示最近7天的数据
            document.getElementById('7day-btn').onclick = function () {
                updateDashboardData('7day');                                 // 调用数据更新函数，传入'7day'类型
            };

            // 功能：30天按钮点击事件处理
            // 说明：用户点击"30天"按钮时，显示最近30天的数据
            document.getElementById('30day-btn').onclick = function () {
                updateDashboardData('30day');                                // 调用数据更新函数，传入'30day'类型
            };

            // 功能：日按钮点击事件处理
            // 说明：用户点击"日"按钮时，显示当天的数据（00:00-23:59）
            document.getElementById('day-btn').onclick = function () {
                updateDashboardData('day');                                  // 调用数据更新函数，传入'day'类型
            };

            // 功能：周按钮点击事件处理
            // 说明：用户点击"周"按钮时，显示本周的数据（周日到周六）
            document.getElementById('week-btn').onclick = function () {
                updateDashboardData('week');                                 // 调用数据更新函数，传入'week'类型
            };

            // 功能：月按钮点击事件处理
            // 说明：用户点击"月"按钮时，显示本月的数据（1号到月末）
            document.getElementById('month-btn').onclick = function () {
                updateDashboardData('month');                                // 调用数据更新函数，传入'month'类型
            };
            
            // ========================================
            // 【用户数据分析 - 自定义日期选择器初始化区域】
            // ========================================

            // 功能：初始化Layui日期选择器组件
            // 说明：创建一个隐藏的日期范围选择器，用于自定义时间范围选择
            laydate.render({
                elem: '#hidden-laydate-input',                                // 绑定到隐藏的输入框元素
                range: true,                                                  // 启用日期范围选择模式（可以选择开始和结束日期）
                trigger: 'click',                                             // 触发方式：点击触发
                done: function (value, date, endDate) {
                    // 功能：日期选择完成后的回调函数
                    // 参数：value - 选择的日期字符串，date - 开始日期对象，endDate - 结束日期对象
                    // 说明：用户选择日期范围后，更新全局变量并刷新数据
                    if (value) {
                        currentTimeType = 'custom';                           // 设置当前时间类型为自定义
                        // 功能：设置开始时间（当天00:00:00）
                        currentDateRange.start = new Date(date.year, date.month - 1, date.date);
                        // 功能：设置结束时间（当天23:59:59.999）
                        currentDateRange.end = new Date(endDate.year, endDate.month - 1, endDate.date, 23, 59, 59, 999);
                        // 功能：使用自定义时间范围更新仪表板数据
                        updateDashboardData('custom', currentDateRange);
                    }
                }
            });

            // 功能：自定义按钮点击事件处理
            // 说明：用户点击"自定义"按钮时，触发隐藏的日期选择器弹出
            document.getElementById('custom-btn').onclick = function () {
                // 功能：程序化触发隐藏输入框的点击事件
                // 说明：通过模拟点击隐藏元素来弹出日期选择器界面
                document.getElementById('hidden-laydate-input').click();
            };
            
            // ========================================
            // 【用户数据分析 - 上一周期按钮事件区域】
            // ========================================

            // 功能：上一周期按钮点击事件处理
            // 说明：用户点击左箭头按钮时，按照预设的顺序切换到上一个时间类型
            // 切换顺序：实时 ← 1天 ← 7天 ← 30天 ← 日 ← 周 ← 月 ← 实时（循环）
            document.getElementById('prev-btn').onclick = function () {
                switch (currentTimeType) {
                    case 'realtime':
                        // 当前是实时模式，上一个是月模式
                        updateDashboardData('month');                         // 切换到月模式
                        break;
                    case '1day':
                        // 当前是1天模式，上一个是实时模式
                        updateDashboardData('realtime');                      // 切换到实时模式
                        break;
                    case '7day':
                        // 当前是7天模式，上一个是1天模式
                        updateDashboardData('1day');                          // 切换到1天模式
                        break;
                    case '30day':
                        // 当前是30天模式，上一个是7天模式
                        updateDashboardData('7day');                          // 切换到7天模式
                        break;
                    case 'day':
                        // 当前是日模式，上一个是30天模式
                        updateDashboardData('30day');                         // 切换到30天模式
                        break;
                    case 'week':
                        // 当前是周模式，上一个是日模式
                        updateDashboardData('day');                           // 切换到日模式
                        break;
                    case 'month':
                        // 当前是月模式，上一个是周模式
                        updateDashboardData('week');                          // 切换到周模式
                        break;
                    default:
                        // 默认情况，不执行任何操作
                        break;
                }
            };

            // ========================================
            // 【用户数据分析 - 下一周期按钮事件区域】
            // ========================================

            // 功能：下一周期按钮点击事件处理
            // 说明：用户点击右箭头按钮时，按照预设的顺序切换到下一个时间类型
            // 切换顺序：实时 → 1天 → 7天 → 30天 → 日 → 周 → 月 → 实时（循环）
            document.getElementById('next-btn').onclick = function () {
                switch (currentTimeType) {
                    case 'realtime':
                        // 当前是实时模式，下一个是1天模式
                        updateDashboardData('1day');                          // 切换到1天模式
                        break;
                    case '1day':
                        // 当前是1天模式，下一个是7天模式
                        updateDashboardData('7day');                          // 切换到7天模式
                        break;
                    case '7day':
                        // 当前是7天模式，下一个是30天模式
                        updateDashboardData('30day');                         // 切换到30天模式
                        break;
                    case '30day':
                        // 当前是30天模式，下一个是日模式
                        updateDashboardData('day');                           // 切换到日模式
                        break;
                    case 'day':
                        // 当前是日模式，下一个是周模式
                        updateDashboardData('week');                          // 切换到周模式
                        break;
                    case 'week':
                        // 当前是周模式，下一个是月模式
                        updateDashboardData('month');                         // 切换到月模式
                        break;
                    case 'month':
                        // 当前是月模式，下一个是实时模式（循环回到开始）
                        updateDashboardData('realtime');                      // 切换到实时模式
                        break;
                    default:
                        // 默认情况，不执行任何操作
                        break;
                }
            };




            // ========================================
            // 【用户数据分析 - 初始化设置区域】
            // ========================================

            // 功能：页面加载完成后的初始化设置
            // 说明：将用户数据分析默认设置为实时模式，并加载对应数据
            updateDashboardData('realtime');                                  // 初始化为实时数据模式
        });

        // ========================================
        // 【Tab切换逻辑区域】
        // ========================================

        // 功能：延迟执行Tab切换事件绑定
        // 说明：使用setTimeout确保DOM元素完全加载后再绑定事件
        setTimeout(function() {
            // 功能：用户数据分析Tab点击事件处理
            // 说明：用户点击"用户数据分析"标签时的处理逻辑
            document.getElementById('user-tab').onclick = function() {
                // 功能：显示用户数据分析区域
                document.getElementById('user-analysis-area').style.display = '';     // 显示用户数据分析内容区域
                // 功能：隐藏资料数据分析区域
                document.getElementById('profile-analysis-area').style.display = 'none'; // 隐藏资料数据分析内容区域
                // 功能：设置当前Tab为选中状态
                this.classList.add('layui-this');                                      // 添加Layui的选中样式类
                // 功能：移除另一个Tab的选中状态
                document.getElementById('profile-tab').classList.remove('layui-this'); // 移除资料数据分析Tab的选中样式
            };

            // 功能：资料数据分析Tab点击事件处理
            // 说明：用户点击"资料数据分析"标签时的处理逻辑
            document.getElementById('profile-tab').onclick = function() {
                // 功能：隐藏用户数据分析区域
                document.getElementById('user-analysis-area').style.display = 'none';   // 隐藏用户数据分析内容区域
                // 功能：显示资料数据分析区域
                document.getElementById('profile-analysis-area').style.display = '';    // 显示资料数据分析内容区域
                // 功能：设置当前Tab为选中状态
                this.classList.add('layui-this');                                       // 添加Layui的选中样式类
                // 功能：移除另一个Tab的选中状态
                document.getElementById('user-tab').classList.remove('layui-this');     // 移除用户数据分析Tab的选中样式
            };
        }, 0);

        // ========================================
        // 资料数据分析区域 ECharts 初始化
        // ========================================
        
        // 初始化资料趋势图实例
        var profileChart = echarts.init(document.getElementById('profile-chart'));
        
        // 资料趋势图配置选项
        var profileChartOption = {
            title: { text: '资料趋势' },  // 图表标题
            tooltip: { trigger: 'axis' },  // 鼠标悬停显示提示框
            legend: { data: ['资料数', '访问量'] },  // 图例数据
            xAxis: { type: 'category', data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'] },  // X轴配置
            yAxis: { type: 'value' },  // Y轴配置
            series: [
                { name: '资料数', type: 'line', data: [120, 132, 101, 134, 90, 230, 210] },  // 资料数数据
                { name: '访问量', type: 'line', data: [220, 182, 191, 234, 290, 330, 310] }  // 访问量数据
            ]
        };
        
        // 应用资料趋势图配置
        profileChart.setOption(profileChartOption);

        // ========================================
        // 资料数据分析区域统计卡片数据填充
        // ========================================
        
        // 填充资料总览数据
        if(document.getElementById('profile-total-docs')) document.getElementById('profile-total-docs').innerText = '1234';
        if(document.getElementById('profile-total-docs-compare')) document.getElementById('profile-total-docs-compare').innerText = '+5%';
        
        // 填充访问量数据
        if(document.getElementById('profile-visits')) document.getElementById('profile-visits').innerText = '5678';
        if(document.getElementById('profile-visits-compare')) document.getElementById('profile-visits-compare').innerText = '+3%';
        
        // 填充浏览量数据
        if(document.getElementById('profile-views')) document.getElementById('profile-views').innerText = '2345';
        if(document.getElementById('profile-views-compare')) document.getElementById('profile-views-compare').innerText = '+2%';
        
        // 填充下载量数据
        if(document.getElementById('profile-downloads')) document.getElementById('profile-downloads').innerText = '345';
        if(document.getElementById('profile-downloads-compare')) document.getElementById('profile-downloads-compare').innerText = '+1%';
        
        // 填充收藏量数据
        if(document.getElementById('profile-favorites')) document.getElementById('profile-favorites').innerText = '456';
        if(document.getElementById('profile-favorites-compare')) document.getElementById('profile-favorites-compare').innerText = '-2%';
        
        // 填充上传量数据
        if(document.getElementById('profile-uploads')) document.getElementById('profile-uploads').innerText = '78';
        if(document.getElementById('profile-uploads-compare')) document.getElementById('profile-uploads-compare').innerText = '+0%';

        // ========================================
        // 资料数据分析区域按钮事件
        // ========================================
        
        // 资料数据分析-实时按钮点击事件
        if(document.getElementById('profile-realtime-btn')) document.getElementById('profile-realtime-btn').onclick = function() {
            alert('资料数据分析-实时 按钮被点击');
        };
        
        // 资料数据分析-1天按钮点击事件
        if(document.getElementById('profile-1day-btn')) document.getElementById('profile-1day-btn').onclick = function() {
            alert('资料数据分析-1天 按钮被点击');
        };
        
        // 资料数据分析-7天按钮点击事件
        if(document.getElementById('profile-7day-btn')) document.getElementById('profile-7day-btn').onclick = function() {
            alert('资料数据分析-7天 按钮被点击');
        };
        
        // 资料数据分析-30天按钮点击事件
        if(document.getElementById('profile-30day-btn')) document.getElementById('profile-30day-btn').onclick = function() {
            alert('资料数据分析-30天 按钮被点击');
        };
        
        // 资料数据分析-日按钮点击事件
        if(document.getElementById('profile-day-btn')) document.getElementById('profile-day-btn').onclick = function() {
            alert('资料数据分析-日 按钮被点击');
        };
        
        // 资料数据分析-周按钮点击事件
        if(document.getElementById('profile-week-btn')) document.getElementById('profile-week-btn').onclick = function() {
            alert('资料数据分析-周 按钮被点击');
        };
        
        // 资料数据分析-月按钮点击事件
        if(document.getElementById('profile-month-btn')) document.getElementById('profile-month-btn').onclick = function() {
            alert('资料数据分析-月 按钮被点击');
        };
        
        // 资料数据分析-自定义按钮点击事件
        if(document.getElementById('profile-custom-btn')) document.getElementById('profile-custom-btn').onclick = function() {
            alert('资料数据分析-自定义 按钮被点击');
        };
        
        // 资料数据分析-上一周期按钮点击事件
        if(document.getElementById('profile-prev-btn')) document.getElementById('profile-prev-btn').onclick = function() {
            switch (currentTimeType) {
                case 'realtime':
                    // 如果是实时，切换到月
                    updateDashboardData('month');
                    break;
                case '1day':
                    // 如果是1天，切换到实时
                    updateDashboardData('realtime');
                    break;
                case '7day':
                    // 如果是7天，切换到1天
                    updateDashboardData('1day');
                    break;
                case '30day':
                    // 如果是30天，切换到7天
                    updateDashboardData('7day');
                    break;
                case 'day':
                    // 如果是日，切换到30天
                    updateDashboardData('30day');
                    break;
                case 'week':
                    // 如果是周，切换到日
                    updateDashboardData('day');
                    break;
                case 'month':
                    // 如果是月，切换到周
                    updateDashboardData('week');
                    break;
                default:
                    break;
            }
        };
        
        // 资料数据分析-下一周期按钮点击事件
        if(document.getElementById('profile-next-btn')) document.getElementById('profile-next-btn').onclick = function() {
            switch (currentTimeType) {
                case 'realtime':
                    // 如果是实时，切换到1天
                    updateDashboardData('1day');
                    break;
                case '1day':
                    // 如果是1天，切换到7天
                    updateDashboardData('7day');
                    break;
                case '7day':
                    // 如果是7天，切换到30天
                    updateDashboardData('30day');
                    break;
                case '30day':
                    // 如果是30天，切换到日
                    updateDashboardData('day');
                    break;
                case 'day':
                    // 如果是日，切换到周
                    updateDashboardData('week');
                    break;
                case 'week':
                    // 如果是周，切换到月
                    updateDashboardData('month');
                    break;
                case 'month':
                    // 如果是月，切换到实时
                    updateDashboardData('realtime');
                    break;
                default:
                    break;
            }
        };
    </script>
</body>

</html>