
var port = "https://localhost:44350";
function getPort() {
    return port;
}
function logint(t) {
    localStorage.setItem('token', t);
}
function liginout() {
    localStorage.removeItem("token");
    location.href = location.href;
}
var base = {
    token: localStorage.getItem('token')
    //href_show: function (title, url, w, h) {
    //    if (title == null || title == '') {
    //        title = false;
    //    };
    //    if (url == null || url == '') {
    //        url = "404.html";
    //    };
    //    if (w == null || w == '') {
    //        w = ($(window).width() * 0.9);
    //    };
    //    if (h == null || h == '') {
    //        h = ($(window).height() - 50);
    //    };
    //    layer.open({
    //        type: 2,
    //        area: [w + 'px', h + 'px'],
    //        fix: false, //不固定
    //        maxmin: true,
    //        shadeClose: true,
    //        shade: 0.4,
    //        title: title,
    //        content: url
    //    });
    //}
}

//时间格式化
Date.prototype.toShortDateString = function () {
    var y = this.getFullYear();
    var m = this.getMonth() + 1;
    m = m < 10 ? '0' + m : m;
    var d = this.getDate();
    d = d < 10 ? ("0" + d) : d;
    return y + "-" + m + "-" + d + " ";
};

Date.prototype.toDateString = function () {
    var y = this.getFullYear();
    var m = this.getMonth() + 1;
    m = m < 10 ? '0' + m : m;
    var d = this.getDate();
    d = d < 10 ? ("0" + d) : d;
    var h = this.getHours();
    h = h < 10 ? ("0" + h) : h;
    var M = this.getMinutes();
    M = M < 10 ? ("0" + M) : M;
    var S = this.getSeconds();
    S = S < 10 ? ("0" + S) : S;
    return y + "-" + m + "-" + d + " " + h + ":" + M + ":" + S;
};

function timestampToTime(timestamp) {
    var date = new Date(timestamp * 1000);
    var Y = date.getFullYear() + '-';
    var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
    var D = date.getDate() + ' ';
    var h = date.getHours() + ':';
    var m = date.getMinutes() + ':';
    var s = date.getSeconds();
    return Y + M + D + h + m + s;
};

function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]); return null;
}

function requestApiGet(post_url, data, result_url) {
    var code = -1;
    $.ajax({
        url: post_url,
        type: "Get",
        async: false,
        data: data,
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        cache: false,
        success: function (result) {
            code = result.code;
            if (result.code == 0) {
                if (result_url != "") {
                    layer.msg(result.msg, {
                        offset: '450px',
                        icon: 1,
                        time: 1000
                    }, function () {
                        location.href = result_url;
                        });
                }
                else {
                    layer.msg(result.msg, {
                        offset: '450px'
                        , icon: 1
                    });
                }
            }
            else {
                if (result.code == 2) {
                    top.location.href = "/Home/Login";
                    return;
                }
                layer.msg(result.msg, {
                    offset: '450px'
                    , icon: 2
                });
            }
        },
        error: function (err) {
            layer.msg('系统错误!', {
                offset: '450px'
                , icon: 2
            });
        }
    });
    return code;
}

function requestApiPost(post_url, data, result_url) {
    var code = -1;
    $.ajax({
        url: post_url,
        type: "Post",
        async: false,
        data: data,
        cache: false,
        success: function (result) {
            code = result.code;
            if (result.code == 0) {
                if (result_url != "") {
                    layer.msg(result.msg, {
                        offset: '450px',
                        icon: 1,
                        time: 2000
                    }, function () {
                        location.href = result_url;
                    });
                }
                else {
                    layer.msg(result.msg, {
                        offset: '450px',
                        icon: 1,
                        time: 2000
                    });
                }
            }
            else {
                if (result.code == 2) {
                    top.location.href = "/Home/Login";
                    return;
                }
                layer.msg(result.msg, {
                    offset: '450px'
                    , icon: 2
                    , time: 2000
                });
            }
        },
        error: function (err) {
            layer.msg('系统错误!', {
                offset: '450px'
                , icon: 2
                , time: 2000
            });
        }
    });
    return code;
}

function requestApiGetData(post_url, data) {
    var json = null;
    $.ajax({
        url: post_url,
        type: "Get",
        async: false,
        data: data,
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        cache: false,
        success: function (result) {
            if (result.code == 2) {
                top.location.href = "/Home/Login";
                return;
            }
            json = result;
        },
        error: function (err) {
            alert("系统错误");
        }
    });
    return json;
}

function requestApiPostData(post_url, data) {
    var json = null;
    $.ajax({
        url: post_url,
        type: "Post",
        async: false,
        data: data,
        cache: false,
        success: function (result) {
            if (result.code == 2) {
                top.location.href = "/Home/Login";
                return;
            }
            json = result;
        },
        error: function (err) {
            alert("系统错误");
        }
    });
    return json;
}

function get_hs_page(cur_page, total, limitcount, display_page) {
    var result = "";
    var curnum_total = 0;//总页数
    if (total > limitcount) {
        curnum_total = parseInt(total / limitcount);
        if (total % limitcount > 0) {
            curnum_total += 1;
        }
    }
    else {
        curnum_total = 1;
    }

    if (curnum_total <= display_page) {
        for (var i = 1; i <= curnum_total; i++) {
            if (cur_page == i) {
                result += `<div class="item checked" style="padding-left: 0px;">${i}</div>`;
            }
            else {
                result += `<div class="item" style="padding-left: 0px;" onclick="GetList(${i}, ${limitcount});">${i}</div>`;
            }
        }
    }
    else {
        var _pageNum = cur_page % display_page;
        if (cur_page > curnum_total) {
            for (var i = 1; i <= _pageNum; i++) {
                var _pageItem = parseInt(parseInt((cur_page - 1) * display_page) + i);
                if (_pageItem <= curnum_total) {
                    if (cur_page == _pageItem) {
                        result += `<div class="item checked" style="padding-left: 0px;">${_pageItem}</div>`;
                    }
                    else {
                        result += `<div class="item" style="padding-left: 0px;" onclick="GetList(${_pageItem}, ${limitcount});">${_pageItem}</div>`;
                    }
                }
            }
        }
        else {
            var row = 1;
            var begin_num = 1;
            while (cur_page > row * display_page) {
                row++;
                begin_num++;
            }
            for (var i = 1; i <= display_page; i++) {
                var _pageItem = parseInt((begin_num - 1) * display_page + i);
                if (_pageItem <= curnum_total) {
                    if (cur_page == _pageItem) {
                        result += `<div class="item checked" style="padding-left: 0px;">${_pageItem}</div>`;
                    }
                    else {
                        result += `<div class="item" style="padding-left: 0px;" onclick="GetList(${_pageItem}, ${limitcount});">${_pageItem}</div>`;
                    }
                }
            }
        }
    }
    return result;
}