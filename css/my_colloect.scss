.left_tool {
    .left_tool-module1 {
        width: 188px;
        height: 273px;
        margin-top: 130px;
        margin-left: 11px;
        background-color: #ffffff;
        border-radius: 8px;
        .title-1 {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding-top: 14px;
            margin-left: 14px;
            margin-bottom: 27px;
            .icon {
                width: 16px;
                height: 18px;
                background-image: url('/img/create/创作中心.png');
                margin-right: 16px;
            }
            .title {
                width: 65px;
                white-space: nowrap;
                height: 18px;
                font-family: AlibabaPuHuiTi_2_65_Medium;
                font-size: 16px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 18px;
                letter-spacing: 1px;
                color: #003f98;
                margin-right: 50px;
            }
            .icon2 {
                width: 10px;
                height: 6px;
                display: flex;
                justify-content: center;
                align-items: center;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }
        .title-2 {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-left: 47px;
            margin-bottom: 15px;
            .title {
                width: 60px;
                height: 18px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 18px;
                letter-spacing: 1px;
                color: #373d45;
            }
            .icon2 {
                margin-left: 8px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
        .title-3 {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-left: 47px;
            margin-bottom: 15px;
            .title {
                font-family: AlibabaPuHuiTi_2_55_Regular;
                font-size: 14px;
                font-weight: normal;
                letter-spacing: 1px;
                color: #757b82;
            }
            .icon3 {
                margin-left: 43px;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 22px;
                height: 22px;
                border-radius: 90%;
                background-color: rgba(15, 114, 252, 0.116);
                font-family: D-DINExp-Bold;
                font-size: 12px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 36px;
                letter-spacing: 0px;
                color: #0f72fc;
            }
        }
    }
    .left_tool-module2 {
        z-index: 999;
        position: absolute;
        .left_tool-module-item {
            width: 188px;
            height: 46px;
            margin-top: 34px;
            margin-left: 11px;
            display: flex;
            justify-content: flex-start;
            justify-items: center;
            border-radius: 8px;
            .icon {
                width: 14px;
                height: 15px;
                margin-left: 15px;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
            .text {
                width: 105px;
                height: 16px;
                margin-left: 18px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                font-size: 16px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 16px;
                letter-spacing: 1px;
                color: #ffffff;
                opacity: 0.7;
            }
            .icon2 {
                width: 6px;
                height: 10px;
            }
        }
    }
    .left_tool-bg {
        width: 188px;
        height: 46px;
        background-color: #ffffff;
        border-radius: 8px;
        position: absolute;
        left: 11px;
        top: 310px;
        z-index: 1;
    }
}

.my_center_module1 {
    width: 1591px;
    height: 76px;
    box-sizing: border-box;
    margin-top: 32px;
    margin-left: 268px;
    background: #FFFFFF;
    border-radius: 10px 0px 0px 0px;
    font-size: 24px;
    font-family: AlibabaPuHuiTi_2_65_Medium;
    font-weight: 400;
    color: #373D45;
    line-height: 76px;
    padding-left: 41px;
    border-bottom: hsla(212, 5%, 48%, 0.188) 1px solid;
}

.set-module1 {
    width: 1591px;
    height: 80px;
    background-color: #ffffff;
    border-radius: 10px 10px 0px 0px;
    margin-top: 32px;
    margin-left: 268px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 999;
    border-bottom: hsla(212, 5%, 48%, 0.114) 1px solid;
    .create-module1-title {
        width: 230px;
        height: 23px;
        margin-left: 42px;
        font-family: AlibabaPuHuiTi_2_75_SemiBold;
        font-size: 24px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 24px;
        letter-spacing: 1px;
        color: #373d45;
    }
    .create-module1-menu {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-right: 42px;
        .create-module1-item {
            width: 138px;
            height: 50px;
            margin-left: 52px;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            background-color: #f7f8fb;
            border-radius: 25px;
            border: solid 1px #edeff3;
            font-family: AlibabaPuHuiTi_2_55_Regular;
            font-size: 18px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 50px;
            letter-spacing: 1px;
            color: #757b82;
            .create-module1-item-icon {
                width: 12px;
                height: 15px;
                margin-right: 10px;
                background: red;
            }
        }
        .create-module1-item-atcive {
            background-color: #f7f8fb;
            border-radius: 25px;
            border: solid 1px #003f98;
        }
    }
}

.set-module2 {
    width: 1591px;
    margin-left: 268px;
    padding-left: 40px;
    padding-top: 37px;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    box-sizing: border-box;
    background-color: #ffffff;
    border-radius: 0px 0px 10px 10px;
    .create-module2-item {
        width: 273px;
        height: 322px;
        position: relative;
        margin-right: 32px;
        margin-bottom: 37px;
        box-sizing: border-box;
        padding-left: 18px;
        padding-top: 18px;
        background-color: #ffffff;
        box-shadow: 0px 18px 32px 0px rgba(100, 110, 135, 0.1);
        border-radius: 10px;
        border: solid 1px #edeff3;
        .create-module2-item-img {
            width: 238px;
            height: 185px;
            background-color: #8c97cb;
            border-radius: 6px;
        }
        .create-module2-item-tip {
            position: absolute;
            left: 18px;
            top: 18px;
            width: 60px;
            height: 25px;
            background-color: #0f72fc;
            border-radius: 6px 0px 10px 0px;
            font-family: AlibabaPuHuiTi_2_65_Medium;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            text-align: center;
            line-height: 25px;
            letter-spacing: 1px;
            color: #ffffff;
        }
        .create-module2-item-title {
            width: 240px;
            height: 41px;
            margin-top: 22px;
            font-family: AlibabaPuHuiTi_2_65_Medium;
            font-size: 18px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 24px;
            letter-spacing: 1px;
            color: #373d45;
        }
        .create-module2-item-more {
            width: 20px;
            height: 6px;
            position: absolute;
            right: 17px;
            bottom: 55px;
            background-image: url('/img/create/更多\ \(1\).png');
        }
        .create-module2-item-bottom {
            margin-top: 20px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding-left: 9px;
            .pic {
                margin-left: -9px;
            }
            .text {
                width: 65px;
                height: 14px;
                margin-left: 14px;
                font-family: D-DINExp-Bold;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 14px;
                letter-spacing: 1px;
                color: #757b82;
            }
            .text2 {
                width: 65px;
                height: 14px;
                margin-left: 40px;
                font-family: D-DINExp-Bold;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 14px;
                letter-spacing: 1px;
                color: #757b82;
                span {
                    color: #fd7949;
                }
            }
        }
        .create-module2-item-hover {
            display: none;
        }
    }
    .create-module2-item:hover {
        .create-module2-item-hover {
            position: absolute;
            top: 0;
            left: 0;
            box-sizing: border-box;
            padding: 50px 0;
            display: block;
            width: 273px;
            height: 324px;
            background-color: #003f98;
            border-radius: 10px;
            opacity: 0.9;
            text-align: center;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            .item {
                width: 100%;
                height: 18px;
                text-align: center;
                font-family: AlibabaPuHuiTi_2_65_Medium;
                font-size: 18px;
                font-weight: normal;
                font-stretch: normal;
                letter-spacing: 1px;
                color: #ffffff;
            }
        }
    }
}