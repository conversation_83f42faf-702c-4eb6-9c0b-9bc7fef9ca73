.left_tool {
    .left_tool-module1 {
        width: 188px;
        height: 207px;
        margin-left: 11px;
        margin-top: 10px;
        margin-bottom: 10px;
        background-color: #ffffff;
        border-radius: 8px;
        .title-1 {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding-top: 14px;
            margin-left: 14px;
            margin-bottom: 27px;
            .icon {
                width: 16px;
                height: 18px;
                background-image: url('/img/set/权限 (1).png');
                margin-right: 16px;
            }
            .title {
                width: 65px;
                white-space: nowrap;
                height: 18px;
                font-family: AlibabaPuHuiTi_2_65_Medium;
                font-size: 16px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 18px;
                letter-spacing: 1px;
                color: #003f98;
                margin-right: 50px;
            }
            .icon2 {
                width: 10px;
                height: 6px;
                display: flex;
                justify-content: center;
                align-items: center;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }
        .title-2 {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-left: 47px;
            margin-bottom: 15px;
            .title {
                width: 60px;
                height: 18px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 18px;
                letter-spacing: 1px;
                color: #373d45;
            }
            .icon2 {
                margin-left: 8px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
        .title-3 {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-left: 47px;
            margin-bottom: 15px;
            .title {
                font-family: AlibabaPuHuiTi_2_55_Regular;
                font-size: 14px;
                font-weight: normal;
                letter-spacing: 1px;
                color: #757b82;
            }
            .icon3 {
                margin-left: 43px;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 22px;
                height: 22px;
                border-radius: 90%;
                background-color: rgba(15, 114, 252, 0.116);
                font-family: D-DINExp-Bold;
                font-size: 12px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 36px;
                letter-spacing: 0px;
                color: #0f72fc;
            }
        }
    }
    .left_tool-module2 {
        .left_tool-module-item {
            width: 188px;
            height: 46px;
            margin-top: 34px;
            margin-left: 11px;
            display: flex;
            justify-content: flex-start;
            justify-items: center;
            border-radius: 8px;
            .icon {
                width: 14px;
                height: 15px;
                margin-left: 15px;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
            .text {
                width: 105px;
                height: 16px;
                margin-left: 18px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                font-size: 16px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 16px;
                letter-spacing: 1px;
                color: #ffffff;
                opacity: 0.7;
            }
            .icon2 {
                width: 6px;
                height: 10px;
            }
        }
    }
    .left_tool-bg {
        width: 188px;
        height: 46px;
        background-color: #ffffff;
        border-radius: 8px;
        position: absolute;
        left: 11px;
        top: 468px;
        z-index: 1;
    }
}

.pass-module1 {
    width: 1591px;
    height: 80px;
    background-color: #ffffff;
    border-radius: 10px 10px 0px 0px;
    margin-top: 32px;
    margin-left: 268px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-family: AlibabaPuHuiTi_2_75_SemiBold;
    font-size: 24px;
    font-weight: normal;
    font-stretch: normal;
    line-height: 24px;
    letter-spacing: 1px;
    color: #373d45;
    span {
        margin-left: 42px;
    }
    .tip {
        width: 80px;
        height: 30px;
        margin-left: 21px;
        text-align: center;
        background-color: #fef3de;
        border-radius: 15px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 30px;
        letter-spacing: 1px;
        color: #f6b023;
    }
}

.pass-module2 {
    width: 1591px;
    margin-top: 32px;
    margin-left: 268px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    box-sizing: border-box;
    border-radius: 0px 0px 10px 10px;

    .pass-module2-left {
        width: 664px;
        height: 930px;
        background-color: #ffffff;

        .news_shen-context-title {
            width: 664px;
            height: 80px;
            box-sizing: border-box;
            padding-left: 42px;
            background-color: #ffffff;
            font-family: AlibabaPuHuiTi_2_75_SemiBold;
            font-size: 22px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 80px;
            letter-spacing: 1px;
            color: #373d45;
            border-bottom: 1px solid hsla(212, 5%, 48%, 0.039);
        }

        .news_shen-context-main {
            width: 664px;
            height: 737px;
            background-color: #ffffff;
            padding-top: 34px;

            .step {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                margin-left: 44px;

                .step-name {
                    width: 120px;
                    height: 15px;
                    font-family: AlibabaPuHuiTi_2_75_SemiBold;
                    font-size: 16px;
                    font-weight: normal;
                    font-stretch: normal;
                    line-height: 25px;
                    letter-spacing: 1px;
                    color: #373d45;
                    text-align: right;
                }

                .step-point {
                    width: 14px;
                    height: 14px;
                    margin: 0 22px;
                    border-radius: 90%;
                    background-color: #f6b023;
                }

                .step-type {
                    width: 81px;
                    height: 35px;
                    text-align: center;
                    background-color: rgba(0, 63, 152, 0.096);
                    border-radius: 18px;
                    font-family: AlibabaPuHuiTi_2_55_Regular;
                    font-size: 16px;
                    font-weight: normal;
                    font-stretch: normal;
                    line-height: 35px;
                    letter-spacing: 1px;
                    color: #003f98;
                }
            }

            .time {
                width: 199px;
                height: 12px;
                margin-top: 22px;
                margin-left: 230px;
                font-family: D-DINExp;
                font-size: 16px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 12px;
                letter-spacing: 1px;
                color: #8290a3;
            }

            .notie {
                width: 323px;
                height: 14px;
                margin-top: 10px;
                margin-left: 230px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 14px;
                letter-spacing: 1px;
                color: #757b82;
            }

            .text {
                width: 472px;
                height: 61px;
                margin-top: 21px;
                margin-left: 230px;
                margin-bottom: 55px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 24px;
                letter-spacing: 1px;
                color: #373d45;
            }

            .inputbox {
                width: 478px;
                height: 112px;
                margin-top: 21px;
                margin-left: 230px;
                box-sizing: border-box;
                padding-top: 13px;
                padding-left: 14px;
                background-color: #f8f9fb;
                border-radius: 4px;
                border: solid 1px #edeff3;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 24px;
                letter-spacing: 1px;
                color: #373d45;
            }

            .sendbtn {
                width: 477px;
                height: 72px;
                margin-top: 21px;
                margin-left: 230px;
                background-color: #003f98;
                border-radius: 4px;
                text-align: center;
                font-family: AlibabaPuHuiTi_2_75_SemiBold;
                font-size: 22px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 74px;
                letter-spacing: 1px;
                color: #ffffff;
            }
        }
    }

    .pass-module2-right {
        width: 907px;
        height: 930px;
        background-color: #ffffff;

        .news_shen-context-title {
            width: 907px;
            height: 80px;
            box-sizing: border-box;
            padding-left: 42px;
            background-color: #ffffff;
            font-family: AlibabaPuHuiTi_2_75_SemiBold;
            font-size: 22px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 80px;
            letter-spacing: 1px;
            color: #373d45;
            border-bottom: 1px solid hsla(212, 5%, 48%, 0.039);
        }

        .news_shen-context-main {
            width: 907px;
            height: 737px;
            background-color: #ffffff;
            padding-top: 34px;
            padding-left: 43px;

            .pass-module2-right-box1 {
                width: 824px;
                height: 87px;
                margin-bottom: 24px;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .item {
                    width: 400px;
                    height: 87px;
                    box-sizing: border-box;
                    background-color: #f7f8fb;
                    border-radius: 10px;
                    border: solid 1px #edeff3;
                    padding-left: 21px;
                    padding-top: 16px;

                    .title {
                        width: 165px;
                        height: 16px;
                        font-family: AlibabaPuHuiTi_2_55_Regular;
                        font-size: 16px;
                        font-weight: normal;
                        font-stretch: normal;
                        line-height: 24px;
                        letter-spacing: 1px;
                        color: #757b82;
                    }

                    .text {
                        width: 173px;
                        height: 18px;
                        margin-top: 16px;
                        font-family: AlibabaPuHuiTi_2_75_SemiBold;
                        font-size: 18px;
                        font-weight: normal;
                        font-stretch: normal;
                        line-height: 30px;
                        letter-spacing: 1px;
                        color: #373d45;
                    }

                    .btn {
                        text-align: center;
                        margin-right: 24px;
                        font-family: AlibabaPuHuiTi_2_75_SemiBold;
                        font-size: 20px;
                        margin-top: 16px;
                        font-weight: normal;
                        font-stretch: normal;
                        line-height: 24px;
                        letter-spacing: 1px;
                        color: #fd7949;
                    }
                }
            }
        }
    }
}

.pass_all-module1 {
    width: 1591px;
    height: 80px;
    background-color: #ffffff;
    border-radius: 10px 10px 0px 0px;
    margin-top: 32px;
    margin-left: 268px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 999;
    .create-module1-title {
        width: 230px;
        height: 23px;
        margin-left: 42px;
        font-family: AlibabaPuHuiTi_2_75_SemiBold;
        font-size: 24px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 24px;
        letter-spacing: 1px;
        color: #373d45;
    }
    .create-module1-menu {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-right: 42px;
        .create-module1-item {
            width: 138px;
            height: 50px;
            margin-left: 52px;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            background-color: #f7f8fb;
            border-radius: 25px;
            border: solid 1px #edeff3;
            font-family: AlibabaPuHuiTi_2_55_Regular;
            font-size: 18px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 50px;
            letter-spacing: 1px;
            color: #757b82;
            .create-module1-item-icon {
                width: 12px;
                height: 15px;
                margin-right: 10px;
                background: red;
            }
        }
        .create-module1-item-atcive {
            background-color: #f7f8fb;
            border-radius: 25px;
            border: solid 1px #003f98;
        }
    }
}

.pass_all-module2 {
    width: 1591px;
    height: 80px;
    background-color: #ffffff;
    border-radius: 10px 10px 0px 0px;
    margin-left: 268px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 999;
    .create-module1-title {
        width: 230px;
        height: 23px;
        margin-left: 42px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-size: 18px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 24px;
        letter-spacing: 1px;
        color: #373d45;
    }
    .create-module1-menu {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-right: 42px;
        .create-module1-item {
            width: 130px;
            height: 35px;
            margin-left: 52px;
            display: flex;
            justify-content: center;
            align-items: center;
            text-align: center;
            background-color: #f7f8fb;
            border-radius: 25px;
            border: solid 1px #edeff3;
            font-family: AlibabaPuHuiTi_2_55_Regular;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 24px;
            letter-spacing: 1px;
            color: #757b82;
            .create-module1-item-icon {
                width: 12px;
                height: 15px;
                margin-right: 10px;
                background: red;
            }
        }
        .create-module1-item-atcive {
            background-color: #f7f8fb;
            border-radius: 25px;
            border: solid 1px #003f98;
        }
    }
}

.pass_all-module3 {
    width: 1591px;
    height: 130px;
    position: relative;
    margin-left: 268px;
    box-sizing: border-box;
    padding-top: 14px;
    padding-left: 42px;
    background-color: #ffffff;
    display: flex;
    justify-content: flex-start;
    .img {
        width: 138px;
        height: 102px;
        background-image: url(/img/pass/图片.png);
        margin-right: 29px;
    }
    .context {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        align-items: center;
        padding-bottom: 37px;
        .context-title {
            width: 1030px;
            height: 17px;
            font-family: AlibabaPuHuiTi_2_65_Medium;
            font-size: 18px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 24px;
            letter-spacing: 1px;
            color: #373d45;
        }
        .context-time {
            width: 500px;
            height: 13px;
            font-family: AlibabaPuHuiTi_2_55_Regular;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 24px;
            letter-spacing: 1px;
            color: #757b82;
            display: flex;
            justify-content: flex-start;
            .context-time-text {
                margin-right: 60px;
            }
            span {
                font-family: AlibabaPuHuiTi_2_75_SemiBold;
                font-size: 14px;
                font-weight: normal;
                letter-spacing: 1px;
                color: #373d45;
            }
        }
    }
    .tip {
        width: 60px;
        height: 25px;
        text-align: center;
        position: absolute;
        background-color: #f6b023;
        border-radius: 6px 0px 10px 0px;
        font-family: AlibabaPuHuiTi_2_65_Medium;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 24px;
        letter-spacing: 1px;
        color: #ffffff;
    }
    .more {
        width: 40px;
        height: 40px;
        right: 43px;
        top: 45px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 90%;
        position: absolute;
        background-color: #f7f8fb;
        border: solid 1px #edeff3;
    }
}

.content_center-content-2-go {
    width: 1591px;
    height: 80px;
    margin-left: 308px;
    transform: translateX(-40px);
    margin-top: 37px;
    position: relative;
    background-color: #ffffff;
    border-radius: 10px;
    text-align: center;
    .content_center-content-2-go-left {
        width: 16px;
        height: 14px;
        position: absolute;
        top: 33px;
        left: 42px;
        background-color: #757b82;
    }
    .content_center-content-2-go-lt {
        width: 148px;
        height: 15px;
        position: absolute;
        top: 33px;
        left: 82px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 15px;
        letter-spacing: 1px;
        color: #757b82;
    }
    .content_center-content-2-go-right {
        width: 147px;
        height: 15px;
        text-align: right;
        position: absolute;
        top: 33px;
        right: 82px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 15px;
        letter-spacing: 1px;
        color: #003f98;
    }
    .content_center-content-2-go-lr {
        width: 16px;
        height: 14px;
        position: absolute;
        top: 33px;
        right: 42px;
        background-color: #003f98;
    }
    .content_center-content-2-go-num {
        display: flex;
        justify-content: flex-start;
        position: absolute;
        top: 0px;
        right: 605px;
        .item {
            width: 40px;
            height: 40px;
            margin: 20px;
            border-radius: 90%;
            text-align: center;
            line-height: 40px;
            border: solid 1px #dcdcdc;
        }
         :first-child {
            background-color: #003f98;
            color: #ffffff;
        }
    }
}