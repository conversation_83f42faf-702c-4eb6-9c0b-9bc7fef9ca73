﻿@charset "UTF-8";

body {
    padding: 0;
    margin: 0;
    transform-origin: top left;
    background: #f8f9fb;
}

.header {
    width: 1920px;
    height: 80px;
    background-color: #fefefe;
    position: relative;
}

    .header .header-logo {
        width: 400px;
        height: 41px;
        position: absolute;
        top: 20px;
        left: 60px;
    }

    .header .header-menu {
        width: 642px;
        height: 17px;
        padding-top: 31px;
        margin-left: 619px;
        font-family: AlibabaPuHuiTi_2_65_Medium;
        font-size: 18px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 24px;
        letter-spacing: 1px;
        color: #757b82;
        display: flex;
        justify-content: space-between;
    }

    .header .header-item {
        cursor: pointer;
    }

    .header .header-item-active {
        color: #004098;
        position: relative;
    }

        .header .header-item-active::after {
            content: '';
            width: 45px;
            height: 3px;
            border-radius: 0 0 25% 25%;
            position: absolute;
            top: -31px;
            left: 20%;
            background: #004098;
            color: #004098;
        }

    .header .header-search {
        width: 270px;
        height: 40px;
        position: absolute;
        top: 21px;
        right: 336px;
        background-color: #f7f8fb;
        border-radius: 20px;
        border: solid 1px #edeff3;
    }

        .header .header-search .el-input__inner {
            border-radius: 20px;
        }

    .header .header-alarm {
        width: 22px;
        height: 26px;
        position: absolute;
        top: 28px;
        right: 251px;
    }

        .header .header-alarm img {
            width: 100%;
            height: 100%;
        }

        .header .header-alarm .header-alarm-num {
            position: absolute;
            width: 16px;
            height: 16px;
            background: #FD7949;
            border-radius: 50%;
            font-size: 12px;
            font-family: D-DIN Exp;
            font-weight: bold;
            color: #FFFFFF;
            line-height: 16px;
            text-align: center;
            left: 12px;
            top: -5px;
        }

    .header .header-icon {
        width: 42px;
        height: 42px;
        position: absolute;
        top: 18px;
        right: 147px;
        background-color: rgba(117, 123, 130, 0.205);
        box-shadow: 0px 10px 24px 0px rgba(130, 144, 163, 0.39);
        border: solid 4px #ffffff;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .header .hedeer-text {
        overflow: hidden;
        text-overflow: ellipsis;
        width: 62px;
        height: 26px;
        position: absolute;
        top: 32px;
        right: 59px;
        white-space: nowrap;
        font-family: AlibabaPuHuiTi_2_65_Medium;
        font-size: 16px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 24px;
        letter-spacing: 1px;
        color: #8895a7;
    }

.footer {
    background-color: #1f2533;
    margin-top: 61px;
}

    .footer .footer-main {
        width: 1920px;
        height: 187px;
        background-color: #1f2533;
        position: relative;
    }

        .footer .footer-main .footer-menu {
            width: 1000px;
            height: 18px;
            position: absolute;
            top: 56px;
            left: 59px;
            display: flex;
            justify-content: flex-start;
            font-family: AlibabaPuHuiTi_2_65_Medium;
            font-size: 18px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 24px;
            letter-spacing: 1px;
            color: #ffffff;
        }

            .footer .footer-main .footer-menu .footer-menu-item {
                margin-right: 75px;
            }

        .footer .footer-main .footer-numtitle {
            width: 192px;
            height: 14px;
            position: absolute;
            right: 217px;
            top: 32px;
            font-family: AlibabaPuHuiTi_2_55_Regular;
            font-size: 14px;
            text-align: right;
            font-weight: normal;
            font-stretch: normal;
            line-height: 24px;
            letter-spacing: 1px;
            color: #ffffff;
            opacity: 0.5;
        }

        .footer .footer-main .footer-number {
            width: 300px;
            height: 18px;
            position: absolute;
            right: 217px;
            top: 57px;
            text-align: right;
            font-family: D-DINExp;
            font-size: 24px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 24px;
            letter-spacing: 1px;
            color: #ffffff;
        }

        .footer .footer-main .footer-qc1 {
            width: 100px;
            height: 100px;
            position: absolute;
            right: 61px;
            top: 29px;
            background: url(/img/home/<USER>
            background-size: 100% 100%;
        }

        .footer .footer-main .footer-qc2 {
            width: 70px;
            height: 70px;
            position: absolute;
            right: 345px;
            bottom: 21px;
            background: url(/img/home/<USER>
            background-size: 100% 100%;
        }

    .footer .footer-bottom {
        width: 1920px;
        height: 43px;
        background-color: #191e2a;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-size: 12px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 43px;
        text-align: center;
        letter-spacing: 1px;
        color: #ffffff;
        opacity: 0.3;
    }

.left_tool {
    width: 210px;
    height: 1544px;
    background-color: #003f98;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 999;
}

    .left_tool .left_tool-icon {
        width: 50px;
        height: 49px;
        background-image: url("/img/content/535a8041d2fc36993a107fb4407e6b3.png");
        position: absolute;
        top: 16px;
        left: 25px;
    }

    .left_tool .left_tool-line {
        width: 1px;
        height: 34px;
        position: absolute;
        top: 23px;
        left: 89px;
        background-color: #e2e3e5;
        opacity: 0.3;
    }

    .left_tool .left_tool-title {
        width: 60px;
        height: 43px;
        position: absolute;
        top: 18px;
        left: 121px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-size: 20px;
        font-weight: normal;
        font-stretch: normal;
        line-height: 24px;
        letter-spacing: 2px;
        color: #ffffff;
    }
