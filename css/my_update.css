@charset "UTF-8";
.left_tool .left_tool-module1 {
  width: 188px;
  height: 273px;
  margin-top: 130px;
  margin-left: 11px;
  background-color: #ffffff;
  border-radius: 8px;
}

.left_tool .left_tool-module1 .title-1 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-top: 14px;
  margin-left: 14px;
  margin-bottom: 27px;
}

.left_tool .left_tool-module1 .title-1 .icon {
  width: 16px;
  height: 18px;
  background-image: url("/img/create/创作中心.png");
  margin-right: 16px;
}

.left_tool .left_tool-module1 .title-1 .title {
  width: 65px;
  white-space: nowrap;
  height: 18px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 18px;
  letter-spacing: 1px;
  color: #003f98;
  margin-right: 50px;
}

.left_tool .left_tool-module1 .title-1 .icon2 {
  width: 10px;
  height: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.left_tool .left_tool-module1 .title-1 .icon2 img {
  width: 100%;
  height: 100%;
}

.left_tool .left_tool-module1 .title-2 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-left: 47px;
  margin-bottom: 15px;
}

.left_tool .left_tool-module1 .title-2 .title {
  width: 60px;
  height: 18px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 18px;
  letter-spacing: 1px;
  color: #373d45;
}

.left_tool .left_tool-module1 .title-2 .icon2 {
  margin-left: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.left_tool .left_tool-module1 .title-3 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-left: 47px;
  margin-bottom: 15px;
}

.left_tool .left_tool-module1 .title-3 .title {
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 14px;
  font-weight: normal;
  letter-spacing: 1px;
  color: #757b82;
}

.left_tool .left_tool-module1 .title-3 .icon3 {
  margin-left: 43px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 22px;
  height: 22px;
  border-radius: 90%;
  background-color: rgba(15, 114, 252, 0.116);
  font-family: D-DINExp-Bold;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 36px;
  letter-spacing: 0px;
  color: #0f72fc;
}

.left_tool .left_tool-module2 {
  z-index: 999;
  position: absolute;
}

.left_tool .left_tool-module2 .left_tool-module-item {
  width: 188px;
  height: 46px;
  margin-top: 34px;
  margin-left: 11px;
  display: flex;
  justify-content: flex-start;
  justify-items: center;
  border-radius: 8px;
}

.left_tool .left_tool-module2 .left_tool-module-item .icon {
  width: 14px;
  height: 15px;
  margin-left: 15px;
}

.left_tool .left_tool-module2 .left_tool-module-item .icon img {
  width: 100%;
  height: 100%;
}

.left_tool .left_tool-module2 .left_tool-module-item .text {
  width: 105px;
  height: 16px;
  margin-left: 18px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 16px;
  letter-spacing: 1px;
  color: #ffffff;
  opacity: 0.7;
}

.left_tool .left_tool-module2 .left_tool-module-item .icon2 {
  width: 6px;
  height: 10px;
}

.left_tool .left_tool-bg {
  width: 188px;
  height: 46px;
  background-color: #ffffff;
  border-radius: 8px;
  position: absolute;
  left: 11px;
  top: 470px;
  z-index: 1;
}

.my_center_module1 {
  width: 1591px;
  height: 76px;
  box-sizing: border-box;
  margin-top: 32px;
  margin-left: 268px;
  background: #FFFFFF;
  border-radius: 10px 0px 0px 0px;
  font-size: 24px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-weight: 400;
  color: #373D45;
  line-height: 76px;
  padding-left: 41px;
  border-bottom: rgba(116, 122, 129, 0.188) 1px solid;
}

.pass_all-module1 {
  width: 1591px;
  height: 80px;
  background-color: #ffffff;
  border-radius: 10px 10px 0px 0px;
  margin-top: 32px;
  margin-left: 268px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 999;
}

.pass_all-module1 .create-module1-title {
  width: 230px;
  height: 23px;
  margin-left: 42px;
  font-family: AlibabaPuHuiTi_2_75_SemiBold;
  font-size: 24px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #373d45;
}

.pass_all-module1 .create-module1-menu {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-right: 42px;
}

.pass_all-module1 .create-module1-menu .create-module1-item {
  width: 146px;
  height: 50px;
  margin-left: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  background-color: #f7f8fb;
  border-radius: 25px;
  border: solid 1px #edeff3;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 50px;
  letter-spacing: 1px;
  color: #757b82;
}

.pass_all-module1 .create-module1-menu .create-module1-item .create-module1-item-icon {
  width: 12px;
  height: 15px;
  margin-right: 10px;
  background: red;
}

.pass_all-module1 .create-module1-menu .create-module1-item-atcive {
  background-color: #f7f8fb;
  border-radius: 25px;
  border: solid 1px #003f98;
}

.pass_all-module2 {
  width: 1591px;
  height: 80px;
  background-color: #ffffff;
  border-radius: 10px 10px 0px 0px;
  margin-left: 268px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 999;
}

.pass_all-module2 .create-module1-title {
  width: 230px;
  height: 23px;
  margin-left: 42px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #373d45;
}

.pass_all-module2 .create-module1-menu {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-right: 42px;
}

.pass_all-module2 .create-module1-menu .create-module1-item {
  width: 130px;
  height: 35px;
  margin-left: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  background-color: #f7f8fb;
  border-radius: 25px;
  border: solid 1px #edeff3;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #757b82;
}

.pass_all-module2 .create-module1-menu .create-module1-item .create-module1-item-icon {
  width: 12px;
  height: 15px;
  margin-right: 10px;
  background: red;
}

.pass_all-module2 .create-module1-menu .create-module1-item-atcive {
  background-color: #f7f8fb;
  border-radius: 25px;
  border: solid 1px #003f98;
}

.pass_all-module3 {
  width: 1591px;
  height: 130px;
  position: relative;
  margin-left: 268px;
  box-sizing: border-box;
  padding-top: 14px;
  padding-left: 42px;
  background-color: #ffffff;
  display: flex;
  justify-content: flex-start;
}

.pass_all-module3 .img {
  width: 268px;
  height: 102px;
  background-image: url(/img/pass/图片.png);
  margin-right: 29px;
}

.pass_all-module3 .context {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  align-items: center;
  padding-bottom: 37px;
}

.pass_all-module3 .context .context-title {
  width: 1030px;
  height: 17px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #373d45;
}

.pass_all-module3 .context .context-time {
  width: 800px;
  height: 13px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #757b82;
  font-size: 14px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-weight: 400;
  color: #8290A3;
  line-height: 24px;
  display: flex;
  justify-content: flex-start;
}

.pass_all-module3 .context .context-time .context-time-text {
  margin-right: 60px;
}

.pass_all-module3 .context .context-time span {
  font-family: AlibabaPuHuiTi_2_75_SemiBold;
  font-size: 14px;
  font-weight: normal;
  letter-spacing: 1px;
  color: #373D45;
}

.pass_all-module3 .tip {
  width: 60px;
  height: 25px;
  text-align: center;
  position: absolute;
  background-color: #f6b023;
  border-radius: 6px 0px 10px 0px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #ffffff;
}

.pass_all-module3 .more {
  width: 40px;
  height: 40px;
  right: 43px;
  top: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 90%;
  position: absolute;
  background-color: #f7f8fb;
  border: solid 1px #edeff3;
}

.content_center-content-2-go {
  width: 1591px;
  height: 80px;
  margin-left: 308px;
  transform: translateX(-40px);
  margin-top: 37px;
  position: relative;
  background-color: #ffffff;
  border-radius: 10px;
  text-align: center;
}

.content_center-content-2-go .content_center-content-2-go-left {
  width: 16px;
  height: 14px;
  position: absolute;
  top: 33px;
  left: 42px;
  background-color: #757b82;
}

.content_center-content-2-go .content_center-content-2-go-lt {
  width: 148px;
  height: 15px;
  position: absolute;
  top: 33px;
  left: 82px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 15px;
  letter-spacing: 1px;
  color: #757b82;
}

.content_center-content-2-go .content_center-content-2-go-right {
  width: 147px;
  height: 15px;
  text-align: right;
  position: absolute;
  top: 33px;
  right: 82px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 15px;
  letter-spacing: 1px;
  color: #003f98;
}

.content_center-content-2-go .content_center-content-2-go-lr {
  width: 16px;
  height: 14px;
  position: absolute;
  top: 33px;
  right: 42px;
  background-color: #003f98;
}

.content_center-content-2-go .content_center-content-2-go-num {
  display: flex;
  justify-content: flex-start;
  position: absolute;
  top: 0px;
  right: 605px;
}

.content_center-content-2-go .content_center-content-2-go-num .item {
  width: 40px;
  height: 40px;
  margin: 20px;
  border-radius: 90%;
  text-align: center;
  line-height: 40px;
  border: solid 1px #dcdcdc;
}

.content_center-content-2-go .content_center-content-2-go-num :first-child {
  background-color: #003f98;
  color: #ffffff;
}
