.left_tool {
    .left_tool-module1 {
        width: 188px;
        height: 273px;
        margin-top: 130px;
        margin-left: 11px;
        background-color: #ffffff;
        border-radius: 8px;
        .title-1 {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding-top: 14px;
            margin-left: 14px;
            margin-bottom: 27px;
            .icon {
                width: 16px;
                height: 18px;
                background-image: url('/img/create/创作中心.png');
                margin-right: 16px;
            }
            .title {
                width: 65px;
                white-space: nowrap;
                height: 18px;
                font-family: AlibabaPuHuiTi_2_65_Medium;
                font-size: 16px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 18px;
                letter-spacing: 1px;
                color: #003f98;
                margin-right: 50px;
            }
            .icon2 {
                width: 10px;
                height: 6px;
                display: flex;
                justify-content: center;
                align-items: center;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }
        .title-2 {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-left: 47px;
            margin-bottom: 15px;
            .title {
                width: 60px;
                height: 18px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 18px;
                letter-spacing: 1px;
                color: #373d45;
            }
            .icon2 {
                margin-left: 8px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
        .title-3 {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-left: 47px;
            margin-bottom: 15px;
            .title {
                font-family: AlibabaPuHuiTi_2_55_Regular;
                font-size: 14px;
                font-weight: normal;
                letter-spacing: 1px;
                color: #757b82;
            }
            .icon3 {
                margin-left: 43px;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 22px;
                height: 22px;
                border-radius: 90%;
                background-color: rgba(15, 114, 252, 0.116);
                font-family: D-DINExp-Bold;
                font-size: 12px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 36px;
                letter-spacing: 0px;
                color: #0f72fc;
            }
        }
    }
    .left_tool-module2 {
        z-index: 999;
        position: absolute;
        .left_tool-module-item {
            width: 188px;
            height: 46px;
            margin-top: 34px;
            margin-left: 11px;
            display: flex;
            justify-content: flex-start;
            justify-items: center;
            border-radius: 8px;
            .icon {
                width: 14px;
                height: 15px;
                margin-left: 15px;
                img {
                    width: 100%;
                    height: 100%;
                }
            }
            .text {
                width: 105px;
                height: 16px;
                margin-left: 18px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                font-size: 16px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 16px;
                letter-spacing: 1px;
                color: #ffffff;
                opacity: 0.7;
            }
            .icon2 {
                width: 6px;
                height: 10px;
            }
        }
    }
    .left_tool-bg {
        width: 188px;
        height: 46px;
        background-color: #ffffff;
        border-radius: 8px;
        position: absolute;
        left: 11px;
        top: 390px;
        z-index: 1;
    }
}

.my_center_module1 {
    width: 1591px;
    height: 76px;
    box-sizing: border-box;
    margin-top: 32px;
    margin-left: 268px;
    background: #FFFFFF;
    border-radius: 10px 0px 0px 0px;
    font-size: 24px;
    font-family: AlibabaPuHuiTi_2_65_Medium;
    font-weight: 400;
    color: #373D45;
    line-height: 76px;
    padding-left: 41px;
    border-bottom: hsla(212, 5%, 48%, 0.188) 1px solid;
}

.my_data_module1 {
    width: 1591px;
    height: 76px;
    box-sizing: border-box;
    margin-left: 268px;
    margin-top: 32px;
    padding-left: 41px;
    position: relative;
    background: #FFFFFF;
    border-radius: 10px 0px 0px 0px;
    font-size: 24px;
    font-family: AlibabaPuHuiTi_2_65_Medium;
    font-weight: 400;
    color: #003F98;
    line-height: 76px;
    span {
        font-size: 20px;
        padding-left: 68px;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-weight: 400;
        color: #8290A3;
        line-height: 76px;
    }
    .line {
        width: 96px;
        height: 2px;
        position: absolute;
        bottom: 0px;
        left: 40px;
        background: #003F98;
    }
}

.my_data_module2 {
    width: 1591px;
    height: 80px;
    box-sizing: border-box;
    margin-left: 268px;
    padding-left: 41px;
    position: relative;
    background: #FFFFFF;
    border-radius: 10px 0px 0px 0px;
    font-family: AlibabaPuHuiTi_2_65_Medium;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .mune {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .mune-item {
            padding: 0 20px;
            font-size: 16px;
            font-family: AlibabaPuHuiTi_2_55_Regular;
            font-weight: 400;
            color: #757B82;
            line-height: 40px;
            height: 40px;
            background: #FFFFFF;
            border: 1px solid #EDEFF3;
        }
    }
}

.my_data_module3 {
    width: 1591px;
    height: 754px;
    background: #FFFFFF;
    box-sizing: border-box;
    margin-left: 268px;
    padding: 0 41px;
    display: flex;
    justify-content: space-between;
    .my_data_module3-item {
        .title {
            width: 356px;
            height: 60px;
            box-sizing: border-box;
            padding: 0 26px;
            background: hsla(215, 100%, 30%, 0.058);
            display: flex;
            font-size: 18px;
            font-family: AlibabaPuHuiTi_2_75_SemiBold;
            font-weight: 400;
            color: #373D45;
            line-height: 24px;
            justify-content: space-between;
            align-items: center;
            img {
                margin-left: 11px;
            }
        }
        .main {
            width: 355px;
            height: 60px;
            box-sizing: border-box;
            padding: 0 26px;
            background: #FFFFFF;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            font-family: AlibabaPuHuiTi_2_55_Regular;
            font-weight: 400;
            color: #373D45;
            line-height: 60px;
            .main-item {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                .main-item-c {
                    width: 22px;
                    height: 22px;
                    margin-right: 10px;
                    background: #F2F5FA;
                    border-radius: 50%;
                    font-size: 14px;
                    font-family: D-DIN Exp;
                    font-weight: 400;
                    color: #373D45;
                    line-height: 22px;
                    text-align: center;
                }
            }
            img {
                margin-right: 11px;
            }
            span {
                color: #31D09D;
            }
        }
        .main-box {
            border: 1px solid rgba(130, 144, 163, 0.071);
        }
    }
}