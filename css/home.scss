.home {
    width: 1920px;
    position: relative;
    .home-banner {
        width: 1920px;
        height: 700px;
        background-color: #475465;
        img {
            width: 100%;
            height: 100%;
        }
    }
    .home-enter {
        width: 1860px;
        height: 603px;
        margin-left: 60px;
        .home-enter-title {
            width: 150px;
            height: 30px;
            padding-top: 103px;
            font-family: AlibabaPuHuiTi_2_65_Medium;
            font-size: 32px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 100px;
            letter-spacing: 2px;
            color: #373d45;
        }
        .home-enter-menu {
            display: flex;
            justify-content: space-between;
            padding-top: 150px;
            .home-enter-item {
                width: 430px;
                height: 252px;
                background-color: #ffffff;
                border: solid 1px #f1f1f1;
                background-image: url('/img/home/<USER>');
                .home-enter-item-icon {
                    width: 50px;
                    height: 50px;
                    margin-top: 52px;
                    margin-left: 36px;
                    // background-color: #9b5af8;
                    // box-shadow: 0px 10px 13px 0px rgba(155, 90, 248, 0.39);
                    img {
                        width: 100%;
                        height: 100%;
                    }
                }
                .home-enter-item-title {
                    width: 200px;
                    height: 21px;
                    margin-top: 15px;
                    margin-left: 36px;
                    font-family: AlibabaPuHuiTi_2_65_Medium;
                    font-size: 22px;
                    font-weight: normal;
                    font-stretch: normal;
                    letter-spacing: 1px;
                    color: #373d45;
                }
                .home-enter-item-en {
                    width: 200px;
                    height: 14px;
                    margin-top: 11px;
                    margin-left: 36px;
                    font-family: AlibabaPuHuiTi_2_45_Light;
                    font-size: 14px;
                    font-weight: normal;
                    font-stretch: normal;
                    letter-spacing: 1px;
                    color: #8290a3;
                    opacity: 0.6;
                }
                .home-enter-item-arrow {
                    width: 30px;
                    height: 30px;
                    margin-top: 28px;
                    margin-left: 36px;
                    background-color: #003f98;
                    border: solid 1px #e2e7f1;
                    border-radius: 50%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }
            /*.home-enter-item:first-child {
                width: 430px;
                height: 252px;
                background-image: url('/img/home/<USER>');
                background-color: #ffffff;
                background-size: 100% 100%;
                box-shadow: 0px 28px 76px 0px rgba(117, 123, 130, 0.18);
                border: solid 1px #003f98;
                transform: translateY(-30px);
            }*/
            .home-enter-item:hover {
                width: 430px;
                height: 252px;
                background-image: url('/img/home/<USER>');
                background-color: #ffffff;
                background-size: 100% 100%;
                box-shadow: 0px 28px 76px 0px rgba(117, 123, 130, 0.18);
                border: solid 1px #003f98;
                transform: translateY(-30px);
            }
        }
    }
    .home-new {
        width: 1860px;
        height: 854px;
        margin-left: 60px;
        .home-new-title {
            width: 200px;
            height: 30px;
            margin-bottom: 68px;
            font-family: AlibabaPuHuiTi_2_65_Medium;
            font-size: 32px;
            font-weight: normal;
            font-stretch: normal;
            line-height: 24px;
            letter-spacing: 2px;
            color: #373d45;
        }
        .home-new-box {
            width: 1800px;
            height: 227px;
            margin-bottom: 23px;
            background-color: #ffffff;
            border: solid 1px #f1f1f1;
            position: relative;
            .home-new-box-img {
                width: 280px;
                height: 198px;
                position: absolute;
                top: 15px;
                left: 17px;
            }
            .home-new-box-title {
                width: 600px;
                height: 19px;
                position: absolute;
                top: 38px;
                left: 364px;
                font-family: AlibabaPuHuiTi_2_65_Medium;
                font-size: 20px;
                font-weight: normal;
                font-stretch: normal;
                letter-spacing: 1px;
                color: #373d45;
            }
            .home-new-box-text {
                width: 1020px;
                height: 38px;
                position: absolute;
                top: 84px;
                left: 364px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 24px;
                letter-spacing: 1px;
                color: #757b82;
            }
            .home-new-box-bottom {
                width: 600px;
                height: 14px;
                position: absolute;
                bottom: 53px;
                left: 364px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                font-size: 14px;
                font-weight: normal;
                font-stretch: normal;
                line-height: 24px;
                letter-spacing: 1px;
                color: #8290a3;
                display: flex;
                justify-content: flex-start;
                .item {
                    margin-right: 59px;
                }
            }
        }
    }
    .msg {
        width: 398px;
        height: 407px;
        box-sizing: border-box;
        padding-left: 26px;
        padding-top: 10px;
        position: absolute;
        right: 60px;
        top: 81px;
        transform: scale(1);
        z-index: 9999999999999;
        background-color: #ffffff;
        box-shadow: 0px 0px 24px 0px rgba(130, 144, 163, 0.32);
        border-radius: 10px;
        .msg-menu {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .msg-menu-item {
                height: 60px;
                width: 93px;
                text-align: center;
                font-size: 16px;
                font-family: AlibabaPuHuiTi_2_65_Medium;
                font-weight: 400;
                color: #373D45;
                line-height: 43px;
                display: flex;
                justify-content: flex-start;
                align-items: center;
                margin-right: 20px;
                white-space: nowrap;
                span {
                    display: inline-block;
                    margin-left: 8px;
                    width: 22px;
                    height: 22px;
                    background-color: #f1f2f3;
                    font-family: D-DINExp;
                    font-size: 12px;
                    border-radius: 90%;
                    font-weight: normal;
                    font-stretch: normal;
                    line-height: 22px;
                    letter-spacing: 0px;
                    color: #757b82;
                }
            }
            .msg-menu-item-active {
                border-bottom: 2px solid #003F98;
            }
        }
        .msg-item {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            margin-top: 22px;
            .pic {
                width: 38px;
                height: 38px;
                margin-right: 14px;
                background: #2E5FDD;
                border-radius: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
                text-align: center;
            }
            .text {
                width: 297px;
                height: 40px;
                font-size: 16px;
                font-family: AlibabaPuHuiTi_2_55_Regular;
                font-weight: 400;
                color: #373D45;
                line-height: 24px;
                span {
                    color: #003F98;
                }
            }
        }
    }
}

.header-item-active1::after {
    content: '';
    width: 45px;
    height: 3px;
    border-radius: 0 0 25% 25%;
    position: absolute;
    top: -31px;
    left: -10% !important;
    background: #004098;
    color: #004098;
}