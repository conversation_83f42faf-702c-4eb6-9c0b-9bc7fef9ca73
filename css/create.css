﻿@charset "UTF-8";
.left_tool .left_tool-module1 {
  width: 188px;
  height: 273px;
  margin-top: 130px;
  margin-left: 11px;
  background-color: #ffffff;
  border-radius: 8px;
}

.left_tool .left_tool-module1 .title-1 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-top: 14px;
  margin-left: 14px;
  margin-bottom: 27px;
}

.left_tool .left_tool-module1 .title-1 .icon {
  width: 16px;
  height: 18px;
  background-image: url("/img/create/创作中心.png");
  margin-right: 16px;
}

.left_tool .left_tool-module1 .title-1 .title {
  width: 65px;
  white-space: nowrap;
  height: 18px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 18px;
  letter-spacing: 1px;
  color: #003f98;
  margin-right: 50px;
}

.left_tool .left_tool-module1 .title-1 .icon2 {
  width: 10px;
  height: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.left_tool .left_tool-module1 .title-1 .icon2 img {
  width: 100%;
  height: 100%;
}

.left_tool .left_tool-module1 .title-2 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-left: 47px;
  margin-bottom: 15px;
}

.left_tool .left_tool-module1 .title-2 .title {
  width: 60px;
  height: 18px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 18px;
  letter-spacing: 1px;
  color: #373d45;
}

.left_tool .left_tool-module1 .title-2 .icon2 {
  margin-left: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.left_tool .left_tool-module1 .title-3 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-left: 47px;
  margin-bottom: 15px;
}

.left_tool .left_tool-module1 .title-3 .title {
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 14px;
  font-weight: normal;
  letter-spacing: 1px;
  color: #757b82;
}

.left_tool .left_tool-module1 .title-3 .icon3 {
  margin-left: 43px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 22px;
  height: 22px;
  border-radius: 90%;
  background-color: rgba(15, 114, 252, 0.116);
  font-family: D-DINExp-Bold;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 36px;
  letter-spacing: 0px;
  color: #0f72fc;
}

.left_tool .left_tool-module2 {
  z-index: 999;
  position: absolute;
}

.left_tool .left_tool-module2 .left_tool-module-item {
  width: 188px;
  height: 46px;
  margin-top: 34px;
  margin-left: 11px;
  display: flex;
  justify-content: flex-start;
  justify-items: center;
  border-radius: 8px;
}

.left_tool .left_tool-module2 .left_tool-module-item .icon {
  width: 14px;
  height: 15px;
  margin-left: 15px;
}

.left_tool .left_tool-module2 .left_tool-module-item .icon img {
  width: 100%;
  height: 100%;
}

.left_tool .left_tool-module2 .left_tool-module-item .text {
  width: 105px;
  height: 16px;
  margin-left: 18px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 16px;
  letter-spacing: 1px;
  color: #ffffff;
  opacity: 0.7;
}

.left_tool .left_tool-module2 .left_tool-module-item .icon2 {
  width: 6px;
  height: 10px;
}

.left_tool .left_tool-bg {
  width: 188px;
  height: 46px;
  background-color: #ffffff;
  border-radius: 8px;
  position: absolute;
  left: 11px;
  top: 468px;
  z-index: 1;
}

.create-module1 {
  width: 1591px;
  height: 80px;
  background-color: #ffffff;
  border-radius: 10px 10px 0px 0px;
  margin-top: 32px;
  margin-left: 268px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.create-module1 .create-module1-title {
  width: 230px;
  height: 23px;
  margin-left: 42px;
  font-family: AlibabaPuHuiTi_2_75_SemiBold;
  font-size: 24px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #373d45;
}

.create-module1 .create-module1-menu {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-right: 42px;
}

.create-module1 .create-module1-menu .create-module1-item {
  width: 138px;
  height: 50px;
  margin-left: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  background-color: #f7f8fb;
  border-radius: 25px;
  border: solid 1px #edeff3;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 50px;
  letter-spacing: 1px;
  color: #757b82;
}

.create-module1 .create-module1-menu .create-module1-item .create-module1-item-icon {
  width: 12px;
  height: 15px;
  margin-right: 10px;
  background: red;
}

.create-module1 .create-module1-menu .create-module1-item-atcive {
  background-color: #f7f8fb;
  border-radius: 25px;
  border: solid 1px #003f98;
}

.create-module2 {
  width: 1591px;
  margin-top: 32px;
  margin-left: 268px;
  padding-left: 40px;
  padding-top: 37px;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  box-sizing: border-box;
  background-color: #ffffff;
  border-radius: 0px 0px 10px 10px;
}

.create-module2 .create-module2-item {
  width: 273px;
  height: 322px;
  position: relative;
  margin-right: 15px;
  margin-bottom: 37px;
  box-sizing: border-box;
  padding-left: 18px;
  padding-top: 18px;
  background-color: #ffffff;
  box-shadow: 0px 18px 32px 0px rgba(100, 110, 135, 0.1);
  border-radius: 10px;
  border: solid 1px #edeff3;
}

.create-module2 .create-module2-item .create-module2-item-img {
  width: 238px;
  height: 185px;
  background-color: #8c97cb;
  border-radius: 6px;
}

.create-module2 .create-module2-item .create-module2-item-tip {
  position: absolute;
  left: 18px;
  top: 18px;
  width: 60px;
  height: 25px;
  background-color: #0f72fc;
  border-radius: 6px 0px 10px 0px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  text-align: center;
  line-height: 25px;
  letter-spacing: 1px;
  color: #ffffff;
}

.create-module2 .create-module2-item .create-module2-item-title {
  width: 240px;
  height: 41px;
  margin-top: 22px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #373d45;
}

.create-module2 .create-module2-item .create-module2-item-more {
  width: 20px;
  height: 6px;
  position: absolute;
  right: 17px;
  bottom: 55px;
  background-image: url("/img/create/更多 (1).png");
}

.create-module2 .create-module2-item .create-module2-item-bottom {
  margin-top: 20px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 9px;
}

.create-module2 .create-module2-item .create-module2-item-bottom .pic {
  margin-left: -9px;
}

.create-module2 .create-module2-item .create-module2-item-bottom .text {
  width: 65px;
  height: 14px;
  margin-left: 14px;
  font-family: D-DINExp-Bold;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 14px;
  letter-spacing: 1px;
  color: #757b82;
}

.create-module2 .create-module2-item .create-module2-item-bottom .text2 {
  width: 65px;
  height: 14px;
  margin-left: 40px;
  font-family: D-DINExp-Bold;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 14px;
  letter-spacing: 1px;
  color: #757b82;
}

.create-module2 .create-module2-item .create-module2-item-bottom .text2 span {
  color: #fd7949;
}

.create-module2 .create-module2-item .create-module2-item-hover {
  display: none;
}

.create-module2 .create-module2-item:hover .create-module2-item-hover {
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
  padding: 50px 0;
  display: block;
  width: 273px;
  height: 324px;
  background-color: #003f98;
  border-radius: 10px;
  opacity: 0.9;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.create-module2 .create-module2-item:hover .create-module2-item-hover .item {
  width: 100%;
  height: 18px;
  text-align: center;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #ffffff;
}

.create_apply-module1 {
  width: 1591px;
  height: 80px;
  margin-top: 31px;
  margin-left: 268px;
  text-align: center;
  background-color: #ffffff;
  border-radius: 10px 10px 0px 0px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 30px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 80px;
  letter-spacing: 2px;
  color: #373d45;
}

.create_apply-module12 {
  width: 1591px;
  height: 2089px;
  box-sizing: border-box;
  margin-left: 268px;
  padding-left: 40px;
  background-color: #ffffff;
}

.create_apply-module12 .create_apply-module2-title {
  width: 100%;
  height: 23px;
  margin-top: 49px;
  margin-bottom: 37px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 24px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 23px;
  letter-spacing: 1px;
  color: #373d45;
}

.create_apply-module12 .create_apply-module2-box1 {
  width: 1507px;
  height: 64px;
  display: flex;
  justify-content: space-between;
  margin: 22px 0;
}

.create_apply-module12 .create_apply-module2-box1 .item {
  width: 360px;
  height: 64px;
  box-sizing: border-box;
  padding-top: 20px;
  padding-left: 28px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #373d45;
  background-color: #f8f9fb;
  border-radius: 4px;
  border: solid 1px #edeff3;
}

.create_apply-module12 .create_apply-module2-box1 .item span {
  font-family: D-DINExp-Bold;
  font-size: 18px;
  font-weight: normal;
  letter-spacing: 1px;
  color: #8290a3;
}

.create_apply-module12 .create_apply-module2-box1 .item img {
  margin-left: 16px;
}

.create_apply-module12 .create_apply-module2-box2 {
  width: 1507px;
  height: 64px;
  display: flex;
  justify-content: space-between;
  margin: 22px 0;
}

.create_apply-module12 .create_apply-module2-box2 .item {
  width: 742px;
  height: 64px;
  box-sizing: border-box;
  padding-top: 20px;
  padding-left: 28px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #373d45;
  background-color: #f8f9fb;
  border-radius: 4px;
  border: solid 1px #edeff3;
}

.create_apply-module12 .create_apply-module2-box2 .item span {
  font-family: D-DINExp-Bold;
  font-size: 18px;
  font-weight: normal;
  letter-spacing: 1px;
  color: #8290a3;
}

.create_apply-module12 .create_apply-module2-box2 .item img {
  margin-left: 16px;
}

.create_apply-module12 .create_apply-module2-box3 {
  width: 1507px;
  height: 103px;
  display: flex;
  justify-content: space-between;
  margin: 22px 0;
}

.create_apply-module12 .create_apply-module2-box3 .item {
  width: 1507px;
  height: 103px;
  box-sizing: border-box;
  padding-top: 20px;
  padding-left: 28px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 34px;
  letter-spacing: 1px;
  color: #373d45;
  background-color: #f8f9fb;
  border-radius: 4px;
  border: solid 1px #edeff3;
}

.create_apply-module12 .create_apply-module2-box3 .item span {
  font-family: D-DINExp-Bold;
  font-size: 18px;
  font-weight: normal;
  letter-spacing: 1px;
  color: #8290a3;
}

.create_apply-module12 .create_apply-module2-box3 .item img {
  margin: 0 36px;
}

.create_apply-module12 .create_apply-module2-box4 {
  width: 1507px;
  height: 156px;
  display: flex;
  justify-content: space-between;
  margin: 22px 0;
}

.create_apply-module12 .create_apply-module2-box4 .item {
  width: 1507px;
  height: 156px;
  box-sizing: border-box;
  padding-top: 20px;
  padding-left: 28px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 34px;
  letter-spacing: 1px;
  color: #373d45;
  background-color: #f8f9fb;
  border-radius: 4px;
  border: solid 1px #edeff3;
}

.create_apply-module12 .create_apply-module2-box4 .item span {
  font-family: D-DINExp-Bold;
  font-size: 18px;
  font-weight: normal;
  letter-spacing: 1px;
  color: #8290a3;
}

.create_apply-module12 .create_apply-module2-box4 .item img {
  margin: 0 36px;
}

.create_apply-module12 .create_apply-module2-box5 {
  width: 1507px;
  height: 64px;
  display: flex;
  justify-content: space-between;
  margin: 22px 0;
}

.create_apply-module12 .create_apply-module2-box5 .item {
  width: 1507px;
  height: 64px;
  box-sizing: border-box;
  padding-top: 20px;
  padding-left: 28px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 34px;
  letter-spacing: 1px;
  color: #373d45;
  background-color: #f8f9fb;
  border-radius: 4px;
  border: solid 1px #edeff3;
}

.create_apply-module12 .create_apply-module2-box5 .item span {
  font-family: D-DINExp-Bold;
  font-size: 18px;
  font-weight: normal;
  letter-spacing: 1px;
  color: #8290a3;
}

.create_apply-module12 .create_apply-module2-box5 .item img {
  margin: 0 36px;
}

.create_apply-module12 .create_apply-module2-button {
  width: 1508px;
  height: 74px;
  text-align: center;
  background-color: #fd7949;
  box-shadow: 0px 18px 32px 0px rgba(253, 121, 73, 0.47);
  border-radius: 4px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 22px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 74px;
  letter-spacing: 1px;
  color: #ffffff;
}

.create_recyc-module1 {
  width: 1591px;
  height: 80px;
  background-color: #ffffff;
  border-radius: 10px 10px 0px 0px;
  margin-top: 32px;
  margin-left: 268px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.create_recyc-module1 .create-module1-title {
  width: 230px;
  height: 23px;
  margin-left: 42px;
  font-family: AlibabaPuHuiTi_2_75_SemiBold;
  font-size: 24px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #373d45;
}

.create_recyc-module1 .create-module1-menu {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-right: 42px;
}

.create_recyc-module1 .create-module1-menu .create-module1-item {
  width: 138px;
  height: 50px;
  margin-left: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  background-color: #f7f8fb;
  border-radius: 25px;
  border: solid 1px #edeff3;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 50px;
  letter-spacing: 1px;
  color: #757b82;
}

.create_recyc-module1 .create-module1-menu .create-module1-item .create-module1-item-icon {
  width: 12px;
  height: 15px;
  margin: 0 10px;
  background: red;
}

.create_recyc-module1 .create-module1-menu .create-module1-item-atcive {
  background-color: #f7f8fb;
  border-radius: 25px;
  border: solid 1px #003f98;
}

.create_recyc-module2 {
  width: 1591px;
  margin-top: 32px;
  margin-left: 268px;
  padding-left: 40px;
  padding-top: 37px;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  box-sizing: border-box;
  background-color: #ffffff;
  border-radius: 0px 0px 10px 10px;
}

.create_recyc-module2 .create-module2-item {
  width: 273px;
  height: 322px;
  position: relative;
  margin-right: 32px;
  margin-bottom: 37px;
  box-sizing: border-box;
  padding-left: 18px;
  padding-top: 18px;
  background-color: #ffffff;
  box-shadow: 0px 18px 32px 0px rgba(100, 110, 135, 0.1);
  border-radius: 10px;
  border: solid 1px #edeff3;
}

.create_recyc-module2 .create-module2-item .create-module2-item-img {
  width: 238px;
  height: 185px;
  background-color: #8c97cb;
  border-radius: 6px;
}

.create_recyc-module2 .create-module2-item .create-module2-item-tip {
  display: none;
  position: absolute;
  left: 18px;
  top: 18px;
  width: 60px;
  height: 25px;
  background-color: #0f72fc;
  border-radius: 6px 0px 10px 0px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  text-align: center;
  line-height: 25px;
  letter-spacing: 1px;
  color: #ffffff;
}

.create_recyc-module2 .create-module2-item .create-module2-item-title {
  width: 240px;
  height: 41px;
  margin-top: 22px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #373d45;
}

.create_recyc-module2 .create-module2-item .create-module2-item-more {
  width: 20px;
  height: 6px;
  position: absolute;
  right: 17px;
  bottom: 55px;
  background-image: url("/img/create/更多 (1).png");
}

.create_recyc-module2 .create-module2-item .create-module2-item-bottom {
  margin-top: 20px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left: 9px;
}

.create_recyc-module2 .create-module2-item .create-module2-item-bottom .pic {
  margin-left: -9px;
}

.create_recyc-module2 .create-module2-item .create-module2-item-bottom .text {
  width: 65px;
  height: 14px;
  margin-left: 14px;
  font-family: D-DINExp-Bold;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 14px;
  letter-spacing: 1px;
  color: #757b82;
}

.create_recyc-module2 .create-module2-item .create-module2-item-bottom .text2 {
  width: 65px;
  height: 14px;
  margin-left: 40px;
  font-family: D-DINExp-Bold;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 14px;
  letter-spacing: 1px;
  color: #757b82;
}

.create_recyc-module2 .create-module2-item .create-module2-item-bottom .text2 span {
  color: #fd7949;
}

.create_recyc-module2 .create-module2-item .create-module2-item-hover {
  display: none;
}

.create_recyc-module2 .create-module2-item:hover .create-module2-item-hover {
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
  padding: 50px 0;
  display: block;
  width: 273px;
  height: 324px;
  background-color: #003f98;
  border-radius: 10px;
  opacity: 0.9;
  text-align: center;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.create_recyc-module2 .create-module2-item:hover .create-module2-item-hover .item {
  width: 100%;
  height: 18px;
  text-align: center;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #ffffff;
}
