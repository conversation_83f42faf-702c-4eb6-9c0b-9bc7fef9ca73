﻿@charset "UTF-8";
.left_tool .left_tool-module-header {
  width: 188px;
  height: 46px;
  position: relative;
  margin-top: 129px;
  margin-left: 11px;
  background-color: #ffffff;
  border-radius: 8px;
}

.left_tool .left_tool-module-header .icon {
  width: 16px;
  height: 17px;
  position: absolute;
  top: 15px;
  left: 14px;
}

.left_tool .left_tool-module-header .title {
  width: 165px;
  height: 16px;
  position: absolute;
  top: 12px;
  left: 46px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #003f98;
}

.left_tool .left_tool-module {
  width: 188px;
  height: 230px;
  margin-top: 30px;
  margin-left: 11px;
  background-color: #ffffff;
  border-radius: 8px;
}

.left_tool .left_tool-module .left_tool-module-title {
  width: 158px;
  height: 46px;
  margin-left: 15px;
  position: relative;
  border-bottom: 1px #edeff3 solid;
}

.left_tool .left_tool-module .left_tool-module-title .icon {
  width: 16px;
  height: 16px;
  position: absolute;
  top: 15px;
  left: 0px;
}

.left_tool .left_tool-module .left_tool-module-title .title {
  width: 163px;
  height: 16px;
  position: absolute;
  top: 12px;
  left: 33px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #373d45;
}

.left_tool .left_tool-module .left_tool-module-title .add {
  width: 10px;
  height: 10px;
  position: absolute;
  top: 18px;
  right: 0px;
  background-image: url("/img/content/加减组件 加号.png");
}

.left_tool .left_tool-module .left_tool-module-content {
  margin-left: 13px;
  margin-top: 26px;
}

.left_tool .left_tool-module1 {
  height: 260px;
}

.left_tool .left_tool-module1 .left_tool-module-content .item {
  display: flex;
  justify-content: start;
  align-items: center;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 1px;
  color: #757b82;
  margin-bottom: 27px;
}

.left_tool .left_tool-module1 .left_tool-module-content .item .arrow {
  width: 6px;
  height: 8px;
  background-image: url("/img/content/下箭头 (1).png");
  margin-right: 9px;
}

.left_tool .left_tool-module1 .left_tool-module-content .item .fs {
  width: 10px;
  height: 9px;
  background-image: url("/img/content/24gf-folderMinus.png");
  margin-right: 9px;
}

.left_tool .left_tool-module1 .left_tool-module-content .item .text {
  margin-right: 35px;
}

.left_tool .left_tool-module2 {
  height: 290px;
}

.left_tool .left_tool-module2 .left_tool-module-content .text {
  width: 160px;
  height: 102px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 30px;
  letter-spacing: 1px;
  color: #757b82;
}

.left_tool .left_tool-module2 .left_tool-module-content .text .item {
  margin-right: 20px;
}

.left_tool .left_tool-module2 .left_tool-module-content .title {
  width: 128px;
  height: 13px;
  margin-top: 35px;
  margin-bottom: 10px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #373d45;
}

.left_tool .left_tool-module3 {
  height: 152px;
}

.left_tool .left_tool-module3 .left_tool-module-content {
  width: 158px;
  height: 34px;
}

.left_tool .left_tool-module4 {
  height: 238px;
}

.left_tool .left_tool-module4 .left_tool-module-content .text {
  width: 160px;
  height: 102px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 30px;
  letter-spacing: 1px;
  color: #757b82;
}

.left_tool .left_tool-module4 .left_tool-module-content .text .item {
  width: 72px;
  height: 34px;
  margin-bottom: 14px;
  text-align: center;
  background-color: #f7f8fb;
  border-radius: 17px;
  border: solid 1px #edeff3;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 34px;
  letter-spacing: 1px;
  color: #8290a3;
}

.content_center-header {
  width: 1591px;
  height: 80px;
  position: relative;
  margin-left: 269px;
  margin-top: 31px;
  background-color: #ffffff;
  border-radius: 10px 10px 0px 0px;
}

.content_center-header .title {
  width: 197px;
  height: 23px;
  position: absolute;
  top: 28px;
  left: 42px;
  font-family: AlibabaPuHuiTi_2_75_SemiBold;
  font-size: 24px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #373d45;
}

.content_center-header .select {
  width: 20px;
  height: 20px;
  position: absolute;
  top: 30px;
  left: 167px;
  background-color: #ffffff;
  border: solid 1px #edeff3;
}

.content_center-header .text {
  width: 180px;
  height: 17px;
  position: absolute;
  top: 28px;
  left: 198px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  color: #373d45;
}

.content_center-header .button-left {
  width: 32px;
  height: 35px;
  position: absolute;
  top: 21px;
  right: 74px;
  background-color: #f7f8fb;
  border-radius: 6px 0px 0px 6px;
  /*border: solid 1px #003f98;*/
}

.content_center-header .button-left .icon {
  position: absolute;
  top: 12px;
  left: 10px;
  width: 12px;
  height: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.content_center-header .button-right {
  width: 32px;
  height: 35px;
  position: absolute;
  top: 21px;
  right: 39px;
  background-color: #f7f8fb;
  border-radius: 0px 6px 6px 0px;
  /*border: solid 1px #edeff3;*/
}

.content_center-header .button-right .icon {
  position: absolute;
  top: 12px;
  left: 10px;
  width: 12px;
  height: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.content_center-header .sort {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: absolute;
  top: 25px;
  left: 973px;
}

.content_center-header .sort .item {
  width: 98px;
  height: 35px;
  margin-right: 30px;
  background-color: #f7f8fb;
  border-radius: 16px;
  border: solid 1px #edeff3;
  position: relative;
}

.content_center-header .sort .item .icon {
  width: 12px;
  height: 15px;
  position: absolute;
  top: 10px;
  left: 12px;
  background-image: url("/img/content/矩形 30.png");
}

.content_center-header .sort .item .text {
  width: 80px;
  height: 14px;
  position: absolute;
  top: 6px;
  left: 30px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #757b82;
}

.content_center-date {
  width: 676px;
  height: 92px;
  position: absolute;
  top: 191px;
  right: 193px;
  z-index: 999;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: #f7f8fb;
  border-radius: 10px 10px 0px 0px;
  border: solid 1px #edeff3;
}

.content_center-date .content_center-date-box {
  width: 454px;
  height: 46px;
  background-color: #ffffff;
  border-radius: 23px;
  border: solid 1px #003f98;
  text-align: center;
  padding-top: 5px;
}

.content_center-date .content_center-date-box .el-input__inner {
  border: none;
}

.content_center-date .content_center-date-box .el-picker-panel__body-wrapper {
  margin-top: 50px;
  margin-left: 50px;
}

.content_center-date .content_center-date-button {
  width: 120px;
  height: 46px;
  text-align: center;
  background-color: #fd7949;
  border-radius: 23px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 46px;
  letter-spacing: 1px;
  color: #ffffff;
}

.content_center-content {
  width: 1591px;
  height: 887px;
  margin-left: 269px;
  background-color: #ffffff;
  box-sizing: border-box;
  padding-top: 38px;
  padding-left: 42px;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.content_center-content .item {
  width: 234px;
  height: 351px;
  box-sizing: border-box;
  margin-right: 19px;
  margin-bottom: 24px;
  position: relative;
  padding-left: 15px;
  background-color: #ffffff;
  box-shadow: 0px 18px 32px 0px rgba(98, 111, 137, 0.11);
  border-radius: 10px;
  border: solid 1px #edeff3;
}

.content_center-content .item .item-title {
  width: 205px;
  height: 41px;
  margin-top: 19px;
  font-family: AlibabaPuHuiTi_2_65_Medium;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 24px;
  letter-spacing: 1px;
  color: #373d45;
}

.content_center-content .item .item-img {
  width: 203px;
  height: 167px;
  margin-top: 20px;
  background-color: #8c97cb;
  border-radius: 6px;
}

.content_center-content .item .item-text-1 {
  width: 188px;
  height: 14px;
  margin-top: 11px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 30px;
  letter-spacing: 1px;
  color: #757b82;
}

.content_center-content .item .item-text-2 {
  width: 121px;
  height: 20px;
  margin-top: 11px;
  margin-bottom: 11px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 30px;
  letter-spacing: 1px;
  color: #757b82;
}

.content_center-content .item .item-tips {
  display: flex;
  justify-content: flex-start;
  margin-top: 11px;
  text-align: center;
}

.content_center-content .item .item-tips .item-tips-item {
  width: 36px;
  height: 23px;
  margin-right: 8px;
  background-color: #f7f8fb;
  border: solid 1px #edeff3;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 30px;
  letter-spacing: 1px;
  color: #757b82;
}

.content_center-content .item .item-tips .item-tips-item-active {
  width: 60px;
  height: 23px;
  background-color: rgba(0, 64, 153, 0.1);
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 23px;
  letter-spacing: 1px;
  color: #003f98;
}

.content_center-content .item .item-btn {
  position: absolute;
  right: 15px;
  bottom: 65px;
  width: 23px;
  height: 23px;
  background-color: #fd7949;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
}

.content_center-content .item .item-btn-pop {
  position: absolute;
  right: 15px;
  bottom: 104px;
  width: 83px;
  height: 105px;
  background-color: #fd7949;
  border-radius: 6px;
}

.content_center-content .item .item-btn-pop .item-btn-item {
  width: 100%;
  margin: 13px 11px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.content_center-content .item .item-btn-pop .item-btn-item .icon {
  width: 20px;
  height: 14px;
}

.content_center-content .item .item-btn-pop .item-btn-item .text {
  width: 30px;
  height: 14px;
  margin-left: 15px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 14px;
  letter-spacing: 1px;
  color: #ffffff;
}

.content_center-content .item:hover {
  background-color: #ffffff;
  box-shadow: 0px 18px 32px 0px rgba(0, 63, 152, 0.11);
  border-radius: 10px;
  border: solid 1px #003f98;
}

.content_center-content-2 {
  width: 1591px;
  height: 687px;
  margin-left: 269px;
  background-color: #ffffff;
  box-sizing: border-box;
  padding-top: 38px;
  padding-left: 42px;
}

.content_center-content-2 .content_center-content-2-table {
  width: 100%;
  height: 636px;
  box-sizing: border-box;
}

.content_center-content-2 .content_center-content-2-table .content_center-content-2-item {
  width: 1500px;
  height: 90px;
  box-sizing: border-box;
  background-color: #ffffff;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border-bottom: rgba(130, 144, 163, 0.103) solid 0.1px;
}

.content_center-content-2 .content_center-content-2-table .content_center-content-2-item .content_center-content-2-select {
  width: 20px;
  height: 20px;
  background-color: #ffffff;
  border: solid 1px #edeff3;
}

.content_center-content-2 .content_center-content-2-table .content_center-content-2-item .content_center-content-2-number {
  width: 15px;
  height: 13px;
  margin-left: 34px;
  font-family: D-DINExp;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 13px;
  letter-spacing: 1px;
  color: #373d45;
}

.content_center-content-2 .content_center-content-2-table .content_center-content-2-item .content_center-content-2-title {
  width: 200px;
  height: 17px;
  margin-left: 25px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 17px;
  letter-spacing: 1px;
  color: #373d45;
}

.content_center-content-2 .content_center-content-2-table .content_center-content-2-item .content_center-content-2-text {
  width: 200px;
  height: 14px;
  margin-left: 106px;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 14px;
  letter-spacing: 1px;
  color: #8290a3;
}

.content_center-content-2 .content_center-content-2-table .content_center-content-2-item .content_center-content-2-tips {
  height: 23px;
  margin-right: 21px;
  background-color: #f7f8fb;
  border: solid 1px #edeff3;
  font-family: AlibabaPuHuiTi_2_55_Regular;
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  line-height: 23px;
  text-align: center;
  letter-spacing: 1px;
  color: #757b82;
}

.content_center-content-2 .content_center-content-2-table .content_center-content-2-item .content_center-content-2-icon {
  width: 4px;
  height: 15px;
  margin-left: 36px;
  background-color: #8290a3;
}