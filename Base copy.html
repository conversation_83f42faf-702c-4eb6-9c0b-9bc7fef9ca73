﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title></title>
    <link rel="stylesheet" href="./layui/css/layui.css">
    <style>
        body {
            margin: 0;
            padding: 15px;
            background-color: #f2f2f2;
        }
        .layui-card-header {
            font-size: 16px;
            font-weight: bold;
        }
        .data-overview {
            text-align: center;
            padding: 20px 0;
        }
        .data-value {
            font-size: 28px;
            color: #333;
            margin-bottom: 5px;
        }
        .data-compare {
            font-size: 14px;
            color: #999;
        }
        .layui-col-md2, .layui-col-md10 {
            padding: 5px;
            
        }
        .layui-tab-brief > .layui-tab-title .layui-this {
            color: #009688;
        }
        .layui-tab-brief > .layui-tab-title .layui-this:after {
            border-bottom: 2px solid #009688;
        }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-tab layui-tab-brief">
                    <ul class="layui-tab-title">
                        <li class="layui-this">用户数据分析</li>
                        <li>资料数据分析</li>
                        <!-- Add more navigation items if needed -->
                    </ul>
                </div>
            </div>

            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-body">
                        <div class="layui-form layui-form-pane">
                            <div class="layui-form-item" style="align-items: center;display: flex;">
                                <label class="layui-form-label">统计时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" class="layui-input" id="test-laydate-range-date" placeholder="请选择日期">
                                </div>
                                <div class="layui-input-inline" style="display: flex;">
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="realtime-btn">实时</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="1day-btn">1天</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="7day-btn">7天</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="30day-btn">30天</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="day-btn">日</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="week-btn">周</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="month-btn">月</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="custom-btn">自定义</button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="prev-btn"><i class="layui-icon layui-icon-left"></i></button>
                                    <button class="layui-btn layui-btn-primary layui-btn-sm" id="next-btn"><i class="layui-icon layui-icon-right"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md12">
                <div class="layui-row layui-col-space15">
                    
                    <!-- 折线图区域 -->
                    <div >
                        <div class="layui-card">
                            <div class="layui-card-header">用户趋势图</div>
                            <div class="layui-card-body">
                                <div id="user-chart" style="height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-col-md12">
                <div class="layui-row layui-col-space15">
                    <!-- 用户总览 -->
                    <div class="layui-col-md2">
                        <div class="layui-card">
                            <div class="layui-card-header">用户总览</div>
                            <div class="layui-card-body data-overview">
                                <div class="data-value" id="total-users">--</div>
                                <div class="data-compare">较前1周: <span id="total-users-compare">--</span></div>
                            </div>
                        </div>
                    </div>
                    <!-- 老用户数 -->
                    <div class="layui-col-md2">
                        <div class="layui-card">
                            <div class="layui-card-header">老用户数</div>
                            <div class="layui-card-body data-overview">
                                <div class="data-value" id="old-users">--</div>
                                <div class="data-compare">较前1周: <span id="old-users-compare">--</span></div>
                            </div>
                        </div>
                    </div>
                    <!-- 新用户数 -->
                    <div class="layui-col-md2">
                        <div class="layui-card">
                            <div class="layui-card-header">新用户数</div>
                            <div class="layui-card-body data-overview">
                                <div class="data-value" id="new-users">--</div>
                                <div class="data-compare">较前1周: <span id="new-users-compare">--</span></div>
                            </div>
                        </div>
                    </div>
                    <!-- 游客用户数量 -->
                    <div class="layui-col-md2">
                        <div class="layui-card">
                            <div class="layui-card-header">游客用户数量</div>
                            <div class="layui-card-body data-overview">
                                <div class="data-value" id="visitor-users">--</div>
                                <div class="data-compare">较前1周: <span id="visitor-users-compare">--</span></div>
                            </div>
                        </div>
                    </div>
                    <!-- 游客注册数量 -->
                    <div class="layui-col-md2">
                        <div class="layui-card">
                            <div class="layui-card-header">游客注册数量</div>
                            <div class="layui-card-body data-overview">
                                <div class="data-value" id="registered-visitors">--</div>
                                <div class="data-compare">较前1周: <span id="registered-visitors-compare">--</span></div>
                            </div>
                        </div>
                    </div>
                    <!-- 经销商 -->
                    <div class="layui-col-md2">
                        <div class="layui-card">
                            <div class="layui-card-header">经销商</div>
                            <div class="layui-card-body data-overview">
                                <div class="data-value" id="dealers">--</div>
                                <div class="data-compare">较前1周: <span id="dealers-compare">--</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="./layui/layui.js"></script>
    <!-- Assuming echarts.min.js is in a 'lib' folder, adjust path if necessary -->
    <script src="./lib/echarts.min.js"></script>
    <script>
        layui.use(['form', 'element', 'laydate'], function(){
            var form = layui.form;
            var element = layui.element;
            var laydate = layui.laydate;
            
            // 当前选中的时间类型
            let currentTimeType = 'realtime';
            // 当前选中的时间范围
            let currentDateRange = {
                start: null,
                end: null
            };

            // Date picker for "统计时间"
            laydate.render({
                elem: '#test-laydate-range-date'
                ,range: true
                ,done: function(value, date, endDate){
                    if(value) {
                        currentTimeType = 'custom';
                        currentDateRange.start = value.split(' - ')[0];
                        currentDateRange.end = value.split(' - ')[1];
                        updateDashboardData(currentTimeType, currentDateRange);
                    }
                }
            });

            // 更新按钮状态
            function updateButtonState(activeType) {
                // 移除所有按钮的选中状态
                document.querySelectorAll('.layui-btn').forEach(btn => {
                    btn.classList.remove('layui-btn-normal');
                    btn.classList.add('layui-btn-primary');
                });
                
                // 为当前选中的按钮添加选中状态
                const activeBtn = document.getElementById(activeType + '-btn');
                if(activeBtn) {
                    activeBtn.classList.remove('layui-btn-primary');
                    activeBtn.classList.add('layui-btn-normal');
                }
            }

            // 处理时间范围
            function handleTimeRange(type) {
                const now = new Date();
                let start, end;
                
                switch(type) {
                    case 'realtime':
                        start = now;
                        end = now;
                        break;
                    case '1day':
                        start = new Date(now - 24 * 60 * 60 * 1000);
                        end = now;
                        break;
                    case '7day':
                        start = new Date(now - 7 * 24 * 60 * 60 * 1000);
                        end = now;
                        break;
                    case '30day':
                        start = new Date(now - 30 * 24 * 60 * 60 * 1000);
                        end = now;
                        break;
                    case 'day':
                        start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                        end = now;
                        break;
                    case 'week':
                        start = new Date(now - (now.getDay() * 24 * 60 * 60 * 1000));
                        end = now;
                        break;
                    case 'month':
                        start = new Date(now.getFullYear(), now.getMonth(), 1);
                        end = now;
                        break;
                }
                
                return { start, end };
            }

            // 更新仪表板数据
            function updateDashboardData(type, dateRange) {
                currentTimeType = type;
                if (dateRange) {
                    currentDateRange = dateRange;
                }
                
                // 更新按钮状态
                updateButtonState(type);
                
                // 更新日期显示
                if (type !== 'custom') {
                    const range = handleTimeRange(type);
                    document.getElementById('test-laydate-range-date').value = 
                        `${range.start.toLocaleDateString()} - ${range.end.toLocaleDateString()}`;
                }
                
                // 这里添加实际的数据获取逻辑
                // ... 原有的数据更新代码 ...
            }

            // 事件监听器
            document.getElementById('realtime-btn').onclick = function() {
                updateDashboardData('realtime');
            };
            document.getElementById('1day-btn').onclick = function() {
                updateDashboardData('1day');
            };
            document.getElementById('7day-btn').onclick = function() {
                updateDashboardData('7day');
            };
            document.getElementById('30day-btn').onclick = function() {
                updateDashboardData('30day');
            };
            document.getElementById('day-btn').onclick = function() {
                updateDashboardData('day');
            };
            document.getElementById('week-btn').onclick = function() {
                updateDashboardData('week');
            };
            document.getElementById('month-btn').onclick = function() {
                updateDashboardData('month');
            };
            document.getElementById('custom-btn').onclick = function() {
                document.getElementById('test-laydate-range-date').click();
            };
            
            // 上一周期
            document.getElementById('prev-btn').onclick = function() {
                const range = handleTimeRange(currentTimeType);
                const duration = range.end - range.start;
                currentDateRange.start = new Date(range.start - duration);
                currentDateRange.end = range.start;
                updateDashboardData('custom', currentDateRange);
            };
            
            // 下一周期
            document.getElementById('next-btn').onclick = function() {
                const range = handleTimeRange(currentTimeType);
                const duration = range.end - range.start;
                currentDateRange.start = range.end;
                currentDateRange.end = new Date(range.end.getTime() + duration);
                updateDashboardData('custom', currentDateRange);
            };

            // 初始化时设置为实时
            updateDashboardData('realtime');
        });
    </script>
</body>
</html>

